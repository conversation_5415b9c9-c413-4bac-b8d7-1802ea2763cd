POST https://dev-connections.kazeel.com/cf/v1/connections/facebook/links
Content-Type: application/json

{
  "userId": "caio-test123452"
}

### generate test links
POST https://dev-connections.kazeel.com/cf/v1/connections/login_test/test-links
Content-Type: application/json

### generate test links (local)
POST http://localhost:8787/cf/v1/connections/google/test-links
Content-Type: application/json

### Simulate prerender request

GET http://localhost:8787/google/l_4c363d8c-b754-4912-9066-b0e9aa87de2a?
    userId=test_user_d105866d-d04f-4c3b-92a8-bae233702fd0
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 GoogleMessages/15.1


### Create hyperbrowser session with proxy

POST http://localhost:8787/v1/internal/hyperbrowser/session
Authorization: Basic ZXhhbXBsZTpleGFtcGxl
Content-Type: application/json

{
  "proxyIp": "http://pg.proxi.es:20000",
  "proxyUsername": "GHCrTBGAgy9xzRU6-s-oLyFJJciaY",
  "proxyPassword": "KqeAAKSQEgxSb5jA",
  "timezone": "America/Chicago",
  "sessionTimeoutMinutes": 120
}

### DEV Create hyperbrowser session with proxy

POST https://dev-connections.kazeel.com/v1/internal/hyperbrowser/session
Authorization: Basic ********************************************************************************
Content-Type: application/json

{
  "proxyIp": "http://pg.proxi.es:20000",
  "proxyUsername": "GHCrTBGAgy9xzRU6-s-oLyFJJciaY",
  "proxyPassword": "KqeAAKSQEgxSb5jA",
  "timezone": "America/Chicago",
  "sessionTimeoutMinutes": 120
}
