import { nodeResolve } from '@rollup/plugin-node-resolve';
import terser from '@rollup/plugin-terser';
import typescript from '@rollup/plugin-typescript';
import crypto from 'crypto';
import fs from 'fs';
import path from 'path';

function generateContentHash(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  return crypto.createHash('md5').update(content).digest('hex').substring(0, 8);
}

function createManifest(entries) {
  const manifest = {};
  entries.forEach((entry) => {
    const hash = generateContentHash(entry.input);
    const baseName = path.basename(entry.output.file, '.min.js');
    const hashedFileName = `${baseName}.${hash}.min.js`;

    manifest[`${baseName}.min.js`] = hashedFileName;
    entry.output.file = `public/out/${hashedFileName}`;
  });

  fs.mkdirSync('public/out', { recursive: true });
  fs.writeFileSync('public/out/manifest.json', JSON.stringify(manifest, null, 2));

  // Also write a TypeScript file that can be imported directly
  const tsContent = `/**
 * This file is auto-generated during the build process.
 * It contains mappings from original filenames to content-hashed versions.
 * DO NOT EDIT MANUALLY.
 */

export const scriptManifest: Record<string, string> = ${JSON.stringify(manifest, null, 2)};`;

  fs.writeFileSync('src/browser/manifest.ts', tsContent);

  return entries;
}

const umdConfig = (inputFile, name) => ({
  input: inputFile,
  output: {
    file: `public/out/${name}.min.js`, // This will be modified by createManifest
    format: 'umd',
    name:
      name
        .replace(/-([a-z])/g, (_, char) => char.toUpperCase())
        .replace(/^./, (c) => c.toUpperCase()) + 'Module',
    inlineDynamicImports: true,
  },
  plugins: [
    typescript({
      tsconfig: false,
      compilerOptions: {
        target: 'es2018',
        module: 'esnext',
        lib: ['es2018', 'dom'],
        declaration: false,
        outDir: undefined,
        strict: true,
        esModuleInterop: true,
        skipLibCheck: true,
        forceConsistentCasingInFileNames: true,
      },
    }),
    nodeResolve({ browser: true }),
    terser(),
  ],
});

const standardConfig = (inputFile, name) => ({
  input: inputFile,
  output: {
    file: `public/out/${name}.min.js`, // This will be modified by createManifest
    format: 'iife',
  },
  plugins: [nodeResolve({ browser: true }), terser()],
});

const configs = [
  standardConfig('src/client/bundles/base-bundle.ts', 'base-bundle'),
  standardConfig('src/client/bundles/captcha-detector-bundle.ts', 'captcha-detector-bundle'),
  standardConfig('src/client/bundles/tab-streamer-bundle.ts', 'tab-streamer-bundle'),
];

export default createManifest(configs);
