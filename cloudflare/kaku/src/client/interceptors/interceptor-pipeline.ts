/**
 * Interceptor Pipeline System
 *
 * Manages sequential processing of video frames through multiple interceptors.
 * Provides a unified interface for chaining multiple interceptors together.
 *
 * Features:
 * - Sequential interceptor processing
 * - Error handling and fallback mechanisms
 * - Performance monitoring for the entire pipeline
 * - Dynamic interceptor addition/removal
 * - Pipeline state management
 */

import type {
  BaseInterceptorInterface,
  InterceptorPipelineInterface,
  InterceptorConfig,
} from '../types';

interface PipelineStats {
  framesProcessed: number;
  errorsEncountered: number;
  averageProcessingTime: number;
  lastProcessingTime: number;
  totalProcessingTime: number;
  interceptorStats: Map<
    string,
    {
      framesProcessed: number;
      totalProcessingTime: number;
      averageProcessingTime: number;
    }
  >;
}

class InterceptorPipeline implements InterceptorPipelineInterface {
  public readonly interceptors: BaseInterceptorInterface[];
  public readonly isInitialized: boolean = false;
  public readonly isEnabled: boolean = true;
  public readonly stats: PipelineStats;

  // Pipeline state
  private processedTrack: MediaStreamTrack | null = null;

  // Transform stream components
  private processor: MediaStreamTrackProcessor | null = null;
  private generator: MediaStreamTrackGenerator | null = null;
  private transformStream: TransformStream<VideoFrame, VideoFrame> | null = null;

  constructor(interceptors: BaseInterceptorInterface[] = []) {
    this.interceptors = [...interceptors];
    (this as any).isInitialized = false;
    (this as any).isEnabled = true;

    // Performance tracking
    this.stats = {
      framesProcessed: 0,
      errorsEncountered: 0,
      averageProcessingTime: 0,
      lastProcessingTime: 0,
      totalProcessingTime: 0,
      interceptorStats: new Map(),
    };

    this.log('InterceptorPipeline created with', this.interceptors.length, 'interceptors');
  }

  /**
   * Initialize the pipeline with a video track
   * @param videoTrack - The input video track to process
   * @returns Promise that resolves to the processed video track
   */
  async initialize(videoTrack: MediaStreamTrack): Promise<MediaStreamTrack> {
    if (this.isInitialized) {
      this.log('Pipeline already initialized, returning existing track');
      return this.processedTrack!;
    }

    this.log('Initializing pipeline with video track');

    try {
      // Create processor and generator
      this.processor = new MediaStreamTrackProcessor({ track: videoTrack });
      this.generator = new MediaStreamTrackGenerator({ kind: 'video' });

      // Create transform stream with bound processing function
      this.transformStream = new TransformStream({
        transform: this.processFrame.bind(this),
      });

      // Connect the pipeline
      this.processor.readable
        .pipeThrough(this.transformStream)
        .pipeTo(this.generator.writable)
        .catch((error) => {
          this.log('Pipeline error:', error);
          this.stats.errorsEncountered++;
        });

      // The generator itself is the processed track
      this.processedTrack = this.generator as any as MediaStreamTrack;
      (this as any).isInitialized = true;

      this.log('Pipeline initialized successfully');
      return this.processedTrack;
    } catch (error) {
      this.log('Error initializing pipeline:', error);
      this.stats.errorsEncountered++;
      throw error;
    }
  }

  /**
   * Process a single video frame through all interceptors
   * @param frame - Input video frame
   * @param controller - Transform controller
   */
  private async processFrame(
    frame: VideoFrame,
    controller: TransformStreamDefaultController<VideoFrame>,
  ): Promise<void> {
    const startTime = performance.now();

    try {
      this.stats.framesProcessed++;

      if (!this.isEnabled || this.interceptors.length === 0) {
        // Pass through original frame if disabled or no interceptors
        controller.enqueue(frame);
        return;
      }
      if (frame == null) {
        // If input frame is null, skip processing
        return;
      }

      let currentFrame = frame;
      const framesToCleanup: VideoFrame[] = [];

      // Process frame through each interceptor sequentially
      for (let i = 0; i < this.interceptors.length; i++) {
        const interceptor = this.interceptors[i];
        if (!interceptor || !interceptor.config.enabled) {
          continue; // Skip disabled interceptors
        }
        console.log('Processing frame through interceptor:', interceptor.name);
        try {
          const interceptorStartTime = performance.now();

          // Process frame through current interceptor
          const processedFrame = await interceptor.processVideoFrame(currentFrame);

          // Track interceptor performance
          const interceptorTime = performance.now() - interceptorStartTime;
          this.updateInterceptorStats(interceptor.name, interceptorTime);

          // If a new frame was created, mark the previous one for cleanup
          if (processedFrame !== currentFrame && currentFrame !== frame) {
            framesToCleanup.push(currentFrame);
          }

          currentFrame = processedFrame;
        } catch (error) {
          this.log(`Error in interceptor ${interceptor.name}:`, error);
          this.stats.errorsEncountered++;
          // Continue with the current frame on error
        }
      }

      // Enqueue the final processed frame
      controller.enqueue(currentFrame);

      // Clean up intermediate frames
      framesToCleanup.forEach((frameToCleanup) => {
        try {
          frameToCleanup.close();
        } catch (error) {
          // Ignore cleanup errors
        }
      });

      // Clean up original frame if it was replaced
      if (currentFrame !== frame) {
        try {
          frame.close();
        } catch (error) {
          // Ignore cleanup errors
        }
      }

      // Update performance stats
      const processingTime = performance.now() - startTime;
      this.stats.lastProcessingTime = processingTime;
      this.stats.totalProcessingTime += processingTime;
      this.stats.averageProcessingTime =
        this.stats.totalProcessingTime / this.stats.framesProcessed;
    } catch (error) {
      this.log('Error processing frame:', error);
      this.stats.errorsEncountered++;
      // Pass through original frame on error
      controller.enqueue(frame);
    }
  }

  /**
   * Update performance statistics for a specific interceptor
   * @param interceptorName - Name of the interceptor
   * @param processingTime - Time taken to process the frame
   */
  private updateInterceptorStats(interceptorName: string, processingTime: number): void {
    let stats = this.stats.interceptorStats.get(interceptorName);
    if (!stats) {
      stats = {
        framesProcessed: 0,
        totalProcessingTime: 0,
        averageProcessingTime: 0,
      };
      this.stats.interceptorStats.set(interceptorName, stats);
    }

    stats.framesProcessed++;
    stats.totalProcessingTime += processingTime;
    stats.averageProcessingTime = stats.totalProcessingTime / stats.framesProcessed;
  }

  /**
   * Add an interceptor to the pipeline
   * @param interceptor - Interceptor to add
   */
  addInterceptor(interceptor: BaseInterceptorInterface): void {
    this.interceptors.push(interceptor);
    this.log(`Added interceptor ${interceptor.name} at end of pipeline`);
  }

  /**
   * Remove an interceptor from the pipeline
   * @param interceptorName - Name of the interceptor to remove
   * @returns True if interceptor was found and removed
   */
  removeInterceptor(interceptorName: string): boolean {
    const index = this.interceptors.findIndex(
      (interceptor) => interceptor.name === interceptorName,
    );

    if (index !== -1) {
      const removed = this.interceptors.splice(index, 1)[0];
      this.log(`Removed interceptor ${removed.name} from pipeline`);
      return true;
    }

    this.log(`Interceptor ${interceptorName} not found in pipeline`);
    return false;
  }

  /**
   * Get an interceptor by name
   * @param interceptorName - Name of the interceptor
   * @returns The interceptor instance or null if not found
   */
  getInterceptor(interceptorName: string): BaseInterceptorInterface | null {
    return this.interceptors.find((interceptor) => interceptor.name === interceptorName) || null;
  }

  /**
   * Enable the entire pipeline
   */
  enable(): void {
    (this as any).isEnabled = true;
    this.log('Pipeline enabled');
  }

  /**
   * Disable the entire pipeline
   */
  disable(): void {
    (this as any).isEnabled = false;
    this.log('Pipeline disabled');
  }

  /**
   * Update configuration for a specific interceptor
   * @param name - Name of the interceptor
   * @param config - Partial configuration to update
   * @returns True if interceptor was found and updated
   */
  updateInterceptorConfig(name: string, config: Partial<InterceptorConfig>): boolean {
    const interceptor = this.getInterceptor(name);
    if (interceptor) {
      try {
        interceptor.updateConfig(config);
        this.log(`Updated config for interceptor: ${name}`);
        return true;
      } catch (error) {
        this.log(`Failed to update config for interceptor ${name}:`, error);
        return false;
      }
    } else {
      this.log(`Interceptor ${name} not found`);
      return false;
    }
  }

  /**
   * Enable or disable the entire pipeline
   * @param enabled - Whether the pipeline should be enabled
   */
  setEnabled(enabled: boolean): void {
    (this as any).isEnabled = enabled;
    this.log(`Pipeline ${enabled ? 'enabled' : 'disabled'}`);
  }

  /**
   * Enable or disable a specific interceptor
   * @param interceptorName - Name of the interceptor
   * @param enabled - Whether the interceptor should be enabled
   */
  setInterceptorEnabled(interceptorName: string, enabled: boolean): void {
    const interceptor = this.getInterceptor(interceptorName);
    if (interceptor) {
      interceptor.config.enabled = enabled;
      this.log(`Interceptor ${interceptorName} ${enabled ? 'enabled' : 'disabled'}`);
    } else {
      this.log(`Interceptor ${interceptorName} not found`);
    }
  }

  /**
   * Get pipeline statistics
   * @returns Current pipeline statistics
   */
  getStats(): PipelineStats {
    return { ...this.stats };
  }

  /**
   * Reset pipeline statistics
   */
  resetStats(): void {
    this.stats.framesProcessed = 0;
    this.stats.errorsEncountered = 0;
    this.stats.averageProcessingTime = 0;
    this.stats.lastProcessingTime = 0;
    this.stats.totalProcessingTime = 0;
    this.stats.interceptorStats.clear();
    this.log('Pipeline statistics reset');
  }

  /**
   * Update interceptor configurations dynamically
   * @param interceptorConfigs - Map of interceptor names to configurations
   */
  readonly updateInterceptorConfigs = (interceptorConfigs: Record<string, any>): void => {
    if (!interceptorConfigs || typeof interceptorConfigs !== 'object') {
      this.log('warn', 'Invalid interceptor configurations provided');
      return;
    }

    this.log('info', 'Updating interceptor configurations:', interceptorConfigs);

    for (const interceptor of this.interceptors) {
      const newConfig = interceptorConfigs[interceptor.name];
      if (newConfig) {
        try {
          interceptor.updateConfig(newConfig);
          this.log('info', `Updated config for interceptor: ${interceptor.name}`);
        } catch (error) {
          this.log('error', `Failed to update config for interceptor ${interceptor.name}:`, error);
        }
      }
    }
  };

  /**
   * Clean up pipeline resources
   */
  async cleanup(): Promise<void> {
    this.log('Cleaning up pipeline resources');

    // Close transform stream
    if (this.transformStream) {
      try {
        await this.transformStream.writable.close();
      } catch (error) {
        // Ignore cleanup errors
      }
      this.transformStream = null;
    }

    // Clean up processor
    if (this.processor) {
      this.processor = null;
    }

    // Clean up generator
    if (this.generator) {
      this.generator = null;
    }

    // Clean up tracks
    this.processedTrack = null;

    (this as any).isInitialized = false;
    this.log('Pipeline cleanup completed');
  }

  /**
   * Logging utility
   * @param args - Arguments to log
   */
  private log(...args: any[]): void {
    console.log('[InterceptorPipeline]', ...args);
  }
}

export default InterceptorPipeline;
