/**
 * CDP Manager - Abstracted Chrome DevTools Protocol Management
 *
 * Provides a clean API for managing CDP connections and event handling
 * Abstracts away the complexity of CDP session management from the control tab
 */

import type { CDPInstance } from '../../browser/simple-cdp';

/**
 * User event types for CDP handling
 */
export interface UserEvent {
  type: 'user-event';
  eventType: 'click' | 'scroll' | 'scrollGesture' | 'keydown' | 'keyup' | 'keypress';
  x?: number;
  y?: number;
  deltaX?: number;
  deltaY?: number;
  key?: string;
  code?: string;
  keyCode?: number;
  text?: string;
  button?: string;
  clickCount?: number;
}

/**
 * CDP Manager Options
 */
export interface CDPManagerOptions {
  wsEndpoint: string;
  debug?: boolean;
  timeout?: number;
  retryAttempts?: number;
}

/**
 * CDP Connection Information
 */
export interface CDPConnection {
  client: CDPInstance;
  sessionId: string;
  createdAt: number;
  isConnected: boolean;
}

/**
 * Tab information retrieved from target
 */
export interface TabInfo {
  width: number;
  height: number;
  url: string;
  title: string;
}

/**
 * Event handler function type
 */
export type EventHandler = (userEvent: UserEvent, targetTabId: string) => Promise<void>;

/**
 * CDP Statistics
 */
export interface CDPStats {
  totalConnections: number;
  eventHandlers: number;
  connections: Array<{
    tabId: string;
    sessionId: string;
    createdAt: number;
  }>;
}

/**
 * CDP Manager - High-level API for CDP operations
 */
export class CDPManager {
  private readonly options: CDPManagerOptions;
  private readonly connections: Map<string, CDPConnection>;
  private readonly eventHandlers: Map<string, EventHandler>;
  private mainCdpClient: CDPInstance | null = null;

  constructor(options: Partial<CDPManagerOptions> = {}) {
    this.options = {
      wsEndpoint: options.wsEndpoint!,
      debug: options.debug || false,
      timeout: options.timeout || 30000,
      retryAttempts: options.retryAttempts || 3,
      ...options,
    };

    this.connections = new Map<string, CDPConnection>();
    this.eventHandlers = new Map<string, EventHandler>();

    this.log('[CDP-Manager] Initialized');
    this.initializeMainCDPClient(this.options.wsEndpoint);
  }

  /**
   * Log messages with CDP-Manager prefix
   */
  private log(message: string, ...args: unknown[]): void {
    if (this.options.debug) {
      console.log(`[CDP-Manager] ${message}`, ...args);
    }
  }

  /**
   * Initialize the main CDP client connection
   * This establishes the primary WebSocket connection to the CDP endpoint
   */
  private async initializeMainCDPClient(wsEndpoint: string): Promise<void> {
    if (this.mainCdpClient) {
      return;
    }

    try {
      // Import CDP dynamically to avoid circular dependencies
      const { CDP } = await import('../../browser/simple-cdp');

      this.mainCdpClient = new CDP({ webSocketDebuggerUrl: wsEndpoint });

      // Enable auto-attach to new targets
      await this.mainCdpClient.Target.setAutoAttach({
        autoAttach: true,
        flatten: true,
        waitForDebuggerOnStart: false,
      });

      this.log('Main CDP client initialized successfully');
    } catch (error) {
      this.log('Failed to initialize main CDP client:', error);
      throw error;
    }
  }

  /**
   * Add a new CDP connection to a target tab
   * Uses the proper CDP protocol pattern: connect to main endpoint, then attach to target
   */
  async addConnection(targetTabId: string): Promise<CDPConnection> {
    try {
      if (this.connections.has(targetTabId)) {
        const existingConnection = this.connections.get(targetTabId)!;
        return existingConnection;
      }

      // Ensure main CDP client is initialized
      await this.initializeMainCDPClient(this.options.wsEndpoint);

      // Attach to the target using the main CDP client
      const attachResult = await this.mainCdpClient!.Target.attachToTarget({
        targetId: targetTabId,
        flatten: true,
      });

      const sessionId = attachResult.sessionId as string;

      // Enable Runtime domain for the target session
      await this.mainCdpClient!.Runtime.enable(undefined, sessionId);

      const connection: CDPConnection = {
        client: this.mainCdpClient!,
        sessionId,
        createdAt: Date.now(),
        isConnected: true,
      };

      this.connections.set(targetTabId, connection);
      this.log(`CDP connection established for tab: ${targetTabId} with session: ${sessionId}`);
      return connection;
    } catch (error) {
      this.log(`Failed to add connection to tab ${targetTabId}:`, error);
      throw error;
    }
  }

  /**
   * Remove a CDP connection
   */
  async removeConnection(targetTabId: string): Promise<void> {
    try {
      const connection = this.connections.get(targetTabId);
      if (!connection) {
        return;
      }

      // Detach from the target if we have a main client
      if (this.mainCdpClient && connection.sessionId) {
        try {
          await this.mainCdpClient.Target.detachFromTarget({
            sessionId: connection.sessionId,
          });
        } catch (detachError) {
          this.log(`Failed to detach from target ${targetTabId}:`, detachError);
        }
      }

      connection.isConnected = false;
      this.connections.delete(targetTabId);
      this.log(`CDP connection removed for tab: ${targetTabId}`);
    } catch (error) {
      this.log(`Failed to remove connection from tab ${targetTabId}:`, error);
      throw error;
    }
  }

  /**
   * Get connection info for a target tab
   */
  getConnection(targetTabId: string): CDPConnection | null {
    return this.connections.get(targetTabId) || null;
  }

  /**
   * Get all active connections
   */
  getAllConnections(): Map<string, CDPConnection> {
    return new Map(this.connections);
  }

  /**
   * Execute a command on a target tab
   */
  async executeCommand(
    targetTabId: string,
    method: string,
    params: Record<string, unknown> = {},
  ): Promise<unknown> {
    try {
      const connection = this.connections.get(targetTabId);
      if (!connection) {
        throw new Error(`No connection found for tab: ${targetTabId}`);
      }

      if (!connection.isConnected) {
        throw new Error(`Connection to tab ${targetTabId} is not active`);
      }

      // Parse method to determine domain and method
      const [domain, methodName] = method.split('.');

      if (!domain || !methodName) {
        throw new Error(`Invalid method format: ${method}. Expected format: Domain.method`);
      }

      // Type-safe access to CDP domains
      const cdpDomain = (connection.client as any)[domain];
      if (!cdpDomain) {
        throw new Error(`Unsupported domain: ${domain}`);
      }

      const cdpMethod = cdpDomain[methodName];
      if (typeof cdpMethod !== 'function') {
        throw new Error(`Unsupported method: ${methodName} in domain: ${domain}`);
      }

      const result = await cdpMethod.call(cdpDomain, params, connection.sessionId);
      return result;
    } catch (error) {
      this.log(`Failed to execute ${method} on tab ${targetTabId}:`, error);
      throw error;
    }
  }

  /**
   * Get target tab information
   */
  async getTargetTabInfo(targetTabId: string): Promise<TabInfo> {
    try {
      const result = await this.executeCommand(targetTabId, 'Runtime.evaluate', {
        expression: `({
          width: window.innerWidth,
          height: window.innerHeight,
          url: window.location.href,
          title: document.title
        })`,
        returnByValue: true,
        awaitPromise: true,
      });

      // Type assertion for CDP Runtime.evaluate result
      const evaluateResult = result as { result: { value: TabInfo } };
      return evaluateResult.result.value;
    } catch (error) {
      this.log(`Failed to get tab info for ${targetTabId}:`, error);
      // Return default values as fallback
      return { width: 1920, height: 1080, url: 'unknown', title: 'Unknown' };
    }
  }

  /**
   * Execute JavaScript on a target tab
   */
  async executeScript(targetTabId: string, script: string): Promise<unknown> {
    try {
      const result = await this.executeCommand(targetTabId, 'Runtime.evaluate', {
        expression: script,
        returnByValue: true,
      });

      // Type assertion for CDP Runtime.evaluate result
      const evaluateResult = result as { result: { value: unknown } };
      return evaluateResult.result.value;
    } catch (error) {
      this.log(`Failed to execute script on tab ${targetTabId}:`, error);
      return null;
    }
  }

  /**
   * Register an event handler for user events
   */
  registerEventHandler(eventType: string, handler: EventHandler): void {
    this.eventHandlers.set(eventType, handler);
  }

  /**
   * Unregister an event handler
   */
  unregisterEventHandler(eventType: string): void {
    this.eventHandlers.delete(eventType);
  }

  /**
   * Handle a user event by dispatching it to the appropriate target tab
   */
  async handleUserEvent(userEvent: UserEvent, targetTabId: string): Promise<void> {
    try {
      // Check if we have a connection to this target tab
      const connection = this.connections.get(targetTabId);
      if (!connection) {
        throw new Error(`No CDP connection found for tab: ${targetTabId}`);
      }

      if (!connection.isConnected) {
        throw new Error(`Connection to tab ${targetTabId} is not active`);
      }

      // Get the appropriate handler for this event type
      const handler = this.eventHandlers.get(userEvent.eventType);
      if (!handler) {
        this.log(`No handler registered for event type: ${userEvent.eventType}`);
        return;
      }

      // Execute the handler
      await handler(userEvent, targetTabId);
    } catch (error) {
      this.log(`Failed to handle user event on tab ${targetTabId}:`, error);
      throw error;
    }
  }

  /**
   * Built-in event handlers
   */

  /**
   * Handle click events
   */
  async handleClickEvent(userEvent: UserEvent, targetTabId: string): Promise<void> {
    try {
      // Get target tab dimensions
      const tabInfo = await this.getTargetTabInfo(targetTabId);
      if (!tabInfo) {
        throw new Error('Could not get target tab info');
      }

      // Validate required coordinates
      if (typeof userEvent.x !== 'number' || typeof userEvent.y !== 'number') {
        throw new Error('Click event requires valid x and y coordinates');
      }

      // Calculate actual coordinates in target tab
      const targetX = userEvent.x * tabInfo.width;
      const targetY = userEvent.y * tabInfo.height;

      // Dispatch mouse down
      const mouseDownParams = {
        type: 'mousePressed',
        x: Math.round(targetX),
        y: Math.round(targetY),
        button: 'left',
        clickCount: 1,
        buttons: 1,
      };

      await this.executeCommand(targetTabId, 'Input.dispatchMouseEvent', mouseDownParams);

      // Small delay between mouse down and up
      await new Promise((resolve) => setTimeout(resolve, 50));

      // Dispatch mouse up
      const mouseUpParams = {
        type: 'mouseReleased',
        x: Math.round(targetX),
        y: Math.round(targetY),
        button: 'left',
        clickCount: 1,
        buttons: 0,
      };

      await this.executeCommand(targetTabId, 'Input.dispatchMouseEvent', mouseUpParams);
    } catch (error) {
      this.log(`Failed to handle click event on tab ${targetTabId}:`, error);
      throw error;
    }
  }

  /**
   * Handle scroll events
   */
  async handleScrollEvent(userEvent: UserEvent, targetTabId: string): Promise<void> {
    try {
      // Get target tab dimensions
      const tabInfo = await this.getTargetTabInfo(targetTabId);
      if (!tabInfo) {
        throw new Error('Could not get target tab info');
      }

      // Validate required coordinates
      if (typeof userEvent.x !== 'number' || typeof userEvent.y !== 'number') {
        throw new Error('Scroll event requires valid x and y coordinates');
      }

      // Calculate actual coordinates in target tab
      const targetX = userEvent.x * tabInfo.width;
      const targetY = userEvent.y * tabInfo.height;

      // Dispatch mouse wheel event
      await this.executeCommand(targetTabId, 'Input.dispatchMouseEvent', {
        type: 'mouseWheel',
        x: Math.round(targetX),
        y: Math.round(targetY),
        deltaX: userEvent.deltaX || 0,
        deltaY: userEvent.deltaY || 0,
      });
    } catch (error) {
      this.log(`Failed to handle scroll event on tab ${targetTabId}:`, error);
      throw error;
    }
  }

  /**
   * Handle key events
   */
  async handleKeyEvent(userEvent: UserEvent, targetTabId: string): Promise<void> {
    try {
      const cdpEventType = this.mapBrowserEventTypeToCDP(userEvent.eventType);

      const cdpParams: Record<string, unknown> = {
        type: cdpEventType,
        key: userEvent.key,
        code: userEvent.code,
        keyCode: userEvent.keyCode,
      };

      if (cdpEventType === 'keyDown' && userEvent.text) {
        cdpParams.text = userEvent.text;
      }

      await this.executeCommand(targetTabId, 'Input.dispatchKeyEvent', cdpParams);
    } catch (error) {
      this.log(`Failed to handle key event on tab ${targetTabId}:`, error);
      throw error;
    }
  }

  /**
   * Handle scroll gesture events using synthesizeScrollGesture (for mobile touch)
   */
  async handleScrollGestureEvent(userEvent: UserEvent, targetTabId: string): Promise<void> {
    try {
      const tabInfo = await this.getTargetTabInfo(targetTabId);
      if (!tabInfo) {
        throw new Error('Could not get target tab info');
      }

      // Validate required coordinates
      if (typeof userEvent.x !== 'number' || typeof userEvent.y !== 'number') {
        throw new Error('Scroll gesture event requires valid x and y coordinates');
      }

      if (typeof userEvent.deltaY !== 'number') {
        throw new Error('Scroll gesture event requires valid deltaY');
      }

      const targetX = userEvent.x * tabInfo.width;
      const targetY = userEvent.y * tabInfo.height;

      // The CDP protocol expects a negative yDistance to scroll DOWN
      // (content moves up), which is the opposite of the touch event's deltaY
      const yDistance = -userEvent.deltaY;

      await this.executeCommand(targetTabId, 'Input.synthesizeScrollGesture', {
        x: Math.round(targetX),
        y: Math.round(targetY),
        yDistance: yDistance,
        xDistance: 0,
        speed: 800,
        gestureSourceType: 'touch',
      });

      this.log(
        `Scroll gesture executed on tab ${targetTabId}: deltaY=${userEvent.deltaY}, yDistance=${yDistance}`,
      );
    } catch (error) {
      this.log(`Failed to handle scroll gesture on tab ${targetTabId}:`, error);
    }
  }

  /**
   * Map browser event types to CDP event types
   */
  private mapBrowserEventTypeToCDP(browserEventType: string): string {
    const eventTypeMap: Record<string, string> = {
      keydown: 'keyDown',
      keyup: 'keyUp',
      keypress: 'keyDown',
    };

    return eventTypeMap[browserEventType] || 'keyDown';
  }

  /**
   * Initialize default event handlers
   */
  initializeDefaultHandlers(): void {
    this.registerEventHandler('click', this.handleClickEvent.bind(this));
    this.registerEventHandler('scroll', this.handleScrollEvent.bind(this));
    this.registerEventHandler('scrollGesture', this.handleScrollGestureEvent.bind(this));
    this.registerEventHandler('keydown', this.handleKeyEvent.bind(this));
    this.registerEventHandler('keyup', this.handleKeyEvent.bind(this));
    this.registerEventHandler('keypress', this.handleKeyEvent.bind(this));
  }

  /**
   * Clean up all connections
   */
  async cleanup(): Promise<void> {
    const cleanupPromises = Array.from(this.connections.keys()).map((targetTabId) =>
      this.removeConnection(targetTabId),
    );

    await Promise.all(cleanupPromises);

    // Close main CDP client if it exists
    if (this.mainCdpClient) {
      try {
        this.mainCdpClient.connection.close();
      } catch (error) {
        this.log('Error closing main CDP client:', error);
      }
      this.mainCdpClient = null;
    }

    this.eventHandlers.clear();
    this.log('CDP Manager cleanup completed');
  }

  /**
   * Get connection statistics
   */
  getStats(): CDPStats {
    return {
      totalConnections: this.connections.size,
      eventHandlers: this.eventHandlers.size,
      connections: Array.from(this.connections.entries()).map(([tabId, conn]) => ({
        tabId,
        sessionId: conn.sessionId,
        createdAt: conn.createdAt,
      })),
    };
  }
}

export default CDPManager;
