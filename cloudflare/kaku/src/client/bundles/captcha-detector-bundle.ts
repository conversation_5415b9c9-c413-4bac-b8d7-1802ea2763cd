import VideoFrameInterceptor from '../interceptors/video-crop-interceptor';
import ChangeDetectorInterceptor from '../interceptors/change-detector-interceptor';

// Register captcha detector interceptors
if (window.controlTabManager) {
  // Register VideoFrameInterceptor (video-crop)
  window.controlTabManager.interceptorRegistry.register('video-crop', VideoFrameInterceptor, {
    debug: true,
    enabled: true,
    enableCropping: true,
    cropRegion: {
      x: 0,
      y: 0,
      width: window.innerWidth,
      height: window.innerHeight,
    },
  });
  window.controlTabManager.interceptorRegistry.register(
    'change-detector',
    ChangeDetectorInterceptor,
    {
      debug: true,
      enabled: false,
    },
  );
} else {
  console.error('[captcha-detector-bundle] controlTabManager does not exist');
}
