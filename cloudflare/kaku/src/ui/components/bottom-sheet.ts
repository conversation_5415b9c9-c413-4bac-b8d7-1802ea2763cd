import { html } from 'hono/html';

/**
 * Renders the "Having Trouble?" bottom sheet content
 * @param isLiveMode - Whether we're in live mode (hides "Take Control" option)
 */
export const renderHelpBottomSheet = (isLiveMode: boolean = false) => {
  return html`
    <div class="p-6 pt-0 text-left">
      ${HelpBottomSheetContent(isLiveMode)}
      <div class="pt-6">
        <button
          id="sheet-back"
          class="bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary-700 w-full dark:bg-primary-700 dark:hover:bg-primary-600"
        >
          Back
        </button>
      </div>
    </div>
  `;
};

export const HelpBottomSheetContent = (isLiveMode: boolean = false) => {
  return html`<div class="font-semibold text-xl mb-2">Having Trouble?</div>
    <div class="text-sm text-surface-on-variant mb-6">If you're stuck, choose an option below:</div>
    <div class="flex flex-col">
      ${!isLiveMode
        ? html`
            <button
              id="sheet-take-control"
              class="flex items-center justify-between px-2 py-4 bg-transparent border-none w-full text-base text-neutral-10 cursor-pointer"
            >
              <span>Take Control</span>
              <span class="text-xl text-neutral-40">→</span>
            </button>
            <div class="h-[1px] bg-neutral-30 mx-6"></div>
          `
        : ''}
      <button
        id="sheet-restart"
        class="flex items-center justify-between px-2 py-4 bg-transparent border-none w-full text-base text-neutral-10 cursor-pointer"
      >
        <span>Restart</span>
        <span class="text-xl text-neutral-40">→</span>
      </button>
      <div class="h-[1px] bg-neutral-30 mx-6"></div>
      <button
        id="sheet-support"
        class="flex items-center justify-between px-2 py-4 bg-transparent border-none w-full text-base text-neutral-10 cursor-pointer"
      >
        <span>Contact Support</span>
        <span class="text-xl text-neutral-40">→</span>
      </button>
    </div>`;
};
