import { WorkflowEntrypoint, type WorkflowEvent, WorkflowStep } from 'cloudflare:workers';
import { ClassificationResult, PageStateResult } from '../agent/types/llm-result';
import {
  initRTCSteaming,
  setupInputFocusListener,
  getHashedScriptUrl,
  injectScript,
  wrapForMainFrameOnly,
} from '../browser';
import {
  ConnectionWorkflowState,
  storeConnectionScreenshotToR2,
} from '../common/utils/storeConnectionScreenshotToR2';
import { GeminiLLMRepository } from '../llm/GeminiLLMRepository';
import { LLMService } from '../llm/LLMService';
import { PlatformTypes } from '../ui/constants';
import { CDPBrowserDataAdapter } from './adapters/CDPBrowserDataAdapter';
import { BrowserStateService } from './BrowserStateService';
import { BrowserServiceFactory, BrowserSession, RemoteBrowserService } from './services';
import {
  ConnectionsWorkflowParams,
  FormSubmissionEventPayload,
  FormSubmissionPayloadSource,
} from './types/ConnectionsWorkflowParams';
import { WorkflowStepName } from './types/WorkflowStepName';
import {
  defaultWorkflowRetryConfig,
  getPlatformVersion,
  K_CUSTOM_VIEWPORT,
  PlatformDetectionConfig,
  platformDetectionConfigs,
  twoFactorChangeDetectionConfig,
} from './utils/constants';
import { generateActionsFromExtractionResult, getMainPrimaryAction, sleep } from './utils/helpers';
import { makeParallelLLMCalls } from '../llm/llm-parallel-calls';
import { CDP } from '../browser/simple-cdp';
import { CoordinatorDOBrowserStateRepository } from './CoordinatorDOBrowserStateRepository';
import { WorkflowStepsManager } from './services/WorkflowStepsManager';
import { BrowserManager } from './services/BrowserManager';
import { Connections } from '../api';
import { FORM_VISION_CLASSIFICATION_PROMPT_V34, FORM_VISION_PROMPT_V6 } from './prompts';
import { GroqLLMRepository } from '../llm/GroqLLMRepository';
import { decryptData, getEncryptionKey } from '../common/utils';
import { PageAttachmentType } from './types/PageAttachmentType';

export class ConnectionsWorkflow extends WorkflowEntrypoint<Env, ConnectionsWorkflowParams> {
  private cdp?: CDP;

  //target tab props
  private targetSessionId?: string;
  private targetId?: string;
  private executionContextId?: number;

  private controlTabTargetId?: string;
  private controlTabSessionId?: string;

  private browserSession?: BrowserSession;

  // Social login detection
  private targetAttachmentPromise?: Promise<PageAttachmentType>;

  private connectionAgentStub?: DurableObjectStub<Connections>;

  // Logging configuration
  private config = {
    debug: true, // Set to true for verbose logging
  };

  private llmService: LLMService = new LLMService({
    primaryRepo: new GeminiLLMRepository(this.env.GEMINI_API_KEY, this.env.AI_GATEWAY_GEMINI_URL),
    secondaryRepo: new GroqLLMRepository(this.env.GROK_API_KEY, this.env.AI_GATEWAY_GROK_URL),
  });

  private browserStateService: BrowserStateService = new BrowserStateService(
    new CoordinatorDOBrowserStateRepository(this.env.CoordinatorDO),
  );
  private browserService: RemoteBrowserService = BrowserServiceFactory.createFromEnvironment(
    this.env,
  );

  private log(...args: any[]): void {
    if (this.config.debug) {
      console.log('[kazeel][connections-workflow]', ...args);
    }
  }

  private warn(...args: any[]): void {
    console.warn('[kazeel][connections-workflow]', ...args);
  }

  private error(...args: any[]): void {
    console.error('[kazeel][connections-workflow]', ...args);
  }

  private isSocialLoginButton(
    pageStateResult: PageStateResult,
    formPayload: FormSubmissionEventPayload,
  ): boolean {
    try {
      // Get the clicked button ID from the form payload
      const clickedButtonId = formPayload.actions.some((action) => action.type === 'click')
        ? formPayload.actions.find((action) => action.type === 'click')!.name
        : null;

      // Find the button in the page state result
      const buttons = pageStateResult.extractionResult.includedControls.buttons;
      const clickedButton = buttons.find((button) => button.id === clickedButtonId);

      return clickedButton?.isSocialLogin ?? false;
    } catch (error) {
      this.error('Error detecting social login button:', error);
      return false;
    }
  }

  /**
   * Set up target attachment listener for social login detection
   * Returns a promise that resolves with the new target ID when a new tab/window is detected
   */
  private setupSocialLoginTargetDetection(): Promise<PageAttachmentType> {
    return new Promise((resolve, reject) => {
      if (!this.cdp) {
        reject(new Error('CDP client not initialized'));
        return;
      }

      this.log('Setting up social login target detection...');

      const timeout = setTimeout(() => {
        this.cdp!.Target.removeEventListener('attachedToTarget', targetListener);
        reject(new Error('Social login target detection timeout'));
      }, 30000); // 30 second timeout

      const targetListener = (event: any) => {
        const { sessionId, targetInfo } = event.params;

        this.log('🎯 New target detected during social login:', {
          sessionId,
          type: targetInfo.type,
          url: targetInfo.url,
          targetId: targetInfo.targetId,
        });

        // Only handle page targets (new tabs/windows)
        if (targetInfo.type === 'page') {
          clearTimeout(timeout);
          this.cdp!.Target.removeEventListener('attachedToTarget', targetListener);
          this.log('✅ Social login target detected:', targetInfo.targetId);

          resolve({ type: 'new-target', targetId: targetInfo.targetId });
        }
      };

      const frameNavigatedListener = (event: any) => {
        const { frame } = event.params;
        if (frame.parentId === undefined) {
          clearTimeout(timeout);
          this.cdp!.Page.removeEventListener('frameNavigated', frameNavigatedListener);
          this.log('✅ Social login target detected:', frame.url);
          resolve({ type: 'new-frame', frameId: frame.id, frameUrl: frame.url });
        }
      };

      this.cdp.Page.addEventListener('frameNavigated', frameNavigatedListener);
      this.cdp.Target.addEventListener('attachedToTarget', targetListener);
    });
  }

  /**
   * Check if URL belongs to a known social login provider
   */
  private isSocialLoginProvider(url: string): boolean {
    const socialLoginProviders = [
      // Google OAuth
      'accounts.google.com',
      'oauth2.googleapis.com',

      // Facebook/Meta
      'facebook.com',
      'm.facebook.com',
      'www.facebook.com',

      // GitHub
      'github.com',
      'www.github.com',

      // Microsoft/Azure
      'login.microsoftonline.com',
      'login.live.com',
      'account.microsoft.com',

      // Twitter/X
      'twitter.com',
      'x.com',
      'api.twitter.com',

      // LinkedIn
      'linkedin.com',
      'www.linkedin.com',

      // Apple
      'appleid.apple.com',
      'idmsa.apple.com',

      // Discord
      'discord.com',
      'discordapp.com',

      // Slack
      'slack.com',

      // Auth0 (common patterns)
      '.auth0.com',

      // Okta (common patterns)
      '.okta.com',
      '.oktapreview.com',

      // Other common OAuth providers
      'auth.atlassian.com',
      'bitbucket.org',
      'gitlab.com',
    ];

    try {
      const urlObj = new URL(url);
      const hostname = urlObj.hostname.toLowerCase();

      return socialLoginProviders.some((provider) => {
        if (provider.startsWith('.')) {
          // Wildcard domain (e.g., .auth0.com)
          return hostname.endsWith(provider);
        } else {
          // Exact domain match
          return hostname === provider || hostname === `www.${provider}`;
        }
      });
    } catch (error) {
      this.log('Error parsing URL for social login detection:', url, error);
      return false;
    }
  }

  /**
   * Handle social login attempt - moved from connection-agent for better separation of concerns
   * This method handles both new-target and new-frame scenarios for social login
   */
  async handleSocialLoginAttempt(target: PageAttachmentType, linkId: string): Promise<void> {
    this.log('Handling social login attempt:', target);

    // Notify connection agent to update state
    if (this.connectionAgentStub) {
      await this.connectionAgentStub.updateSocialLoginState();
    }

    switch (target.type) {
      case 'new-target':
        this.log('New target detected:', target.targetId);
        await this.handleNewTarget(target.targetId, linkId);
        break;
      case 'new-frame':
        this.log('New frame detected:', target.frameId);
        await this.handleNewFrame(target.frameId, target.frameUrl, linkId);
        break;
    }
  }

  /**
   * Handle new target for social login - moved from connection-agent
   */
  private async handleNewTarget(targetId: string, linkId: string): Promise<void> {
    if (!this.cdp) {
      throw new Error('CDP client not initialized');
    }

    const target = await this.cdp.Target.attachToTarget({
      targetId,
      flatten: true,
    });

    this.cdp.Page.enable(undefined, target?.sessionId);
    this.cdp.Runtime.enable(undefined, target?.sessionId);
    this.cdp.Page.setBypassCSP({ enabled: true }, target?.sessionId);

    // Start tab streamer for social login
    this.log('Starting social login tab streamer for new target:', targetId);

    // Phase 1: Inject tab streamer script into the current page
    await this.injectTabStreamerScript(target!.sessionId, linkId);

    // Set up WebRTC streaming for social login
    await this.setupSocialLoginStreaming(target?.sessionId, linkId);

    // Phase 2: Set up persistent script injection for page refreshes/navigation
    await this.setupPersistentTabStreamerInjection(target?.sessionId, linkId);

    // Notify connection agent to update UI state
    if (this.connectionAgentStub) {
      await this.connectionAgentStub.updateSocialLoginUIState();
    }
  }

  /**
   * Handle new frame for social login - moved from connection-agent
   */
  private async handleNewFrame(frameId: string, frameUrl: string, linkId: string): Promise<void> {
    this.log('New frame detected:', frameId, 'URL:', frameUrl);

    try {
      if (frameUrl && this.isSocialLoginProvider(frameUrl)) {
        this.log('Social login provider detected in new frame:', frameId, 'URL:', frameUrl);

        // For same-target social login, we use the main session
        const sessionId = this.targetSessionId;

        if (!sessionId) {
          throw new Error('Target session ID not available');
        }

        // Phase 1: Inject tab streamer script into the current page
        await this.injectTabStreamerScript(sessionId, linkId);

        // Set up WebRTC streaming for social login
        await this.setupSocialLoginStreaming(sessionId, linkId);

        // Phase 2: Set up persistent script injection for page refreshes/navigation
        await this.setupPersistentTabStreamerInjection(sessionId, linkId);

        // Notify connection agent to update UI state
        if (this.connectionAgentStub) {
          await this.connectionAgentStub.updateSocialLoginUIState();
        }

        this.log('Social login streaming setup completed for new frame:', frameId);
      }
    } catch (error) {
      this.error('Error handling new frame for social login detection:', error);
      throw error;
    }
  }

  /**
   * Inject tab streamer script into the social login tab (Phase 1: Immediate injection)
   */
  private async injectTabStreamerScript(sessionId: string, linkId: string): Promise<void> {
    if (!this.cdp) {
      throw new Error('CDP client not initialized');
    }

    try {
      this.log('Phase 1: Injecting tab streamer script into current social login page');
      const tabStreamer = getHashedScriptUrl(this.env.KAKU_API_ENDPOINT, 'tab-streamer.min.js');
      const scriptContent = await fetch(tabStreamer).then((res) => res.text());
      await injectScript(this.cdp, tabStreamer, null, sessionId);
      this.log('Tab streamer script injected successfully into current page', tabStreamer);

      // inject tab streamer with addScriptToEvaluateOnNewDocument (main frame only)
      await this.cdp.Page.addScriptToEvaluateOnNewDocument(
        {
          source: wrapForMainFrameOnly(scriptContent),
          worldName: 'kaku-social-login-world',
        },
        sessionId,
      );
    } catch (error) {
      this.error('Failed to inject tab streamer script into current page:', error);
      throw error;
    }
  }

  /**
   * Set up persistent tab streamer injection (Phase 2: Persistent across navigation)
   */
  private async setupPersistentTabStreamerInjection(
    sessionId: string,
    linkId: string,
  ): Promise<void> {
    if (!this.cdp) {
      throw new Error('CDP client not initialized');
    }

    try {
      this.log('Phase 2: Setting up persistent tab streamer injection for navigation/refresh');

      // Get the tab streamer script content for persistent injection
      const tabStreamerScript = await this.getTabStreamerScriptForPersistentInjection(linkId);

      // Add script to evaluate on new document (persists across navigation, main frame only)
      await this.cdp.Page.addScriptToEvaluateOnNewDocument(
        {
          source: wrapForMainFrameOnly(tabStreamerScript),
          worldName: 'kaku-social-login-world',
        },
        sessionId,
      );

      this.log('Persistent tab streamer injection setup completed');
    } catch (error) {
      this.error('Failed to setup persistent tab streamer injection:', error);
      throw error;
    }
  }

  /**
   * Get tab streamer script content for persistent injection
   */
  private async getTabStreamerScriptForPersistentInjection(linkId: string): Promise<string> {
    // This creates a self-initializing script that will automatically set up
    // tab streaming when injected into new documents during navigation
    return `
      (function() {
        const agentWsEndpoint = '${this.env.KAKU_WS_ENDPOINT}/v1/agents/connections/${linkId}';

        let isStreamingSetup = false;

        async function setupTabStreaming() {
          try {
            if (isStreamingSetup) {
              console.log('[kazeel] Tab streaming already setup, skipping');
              return;
            }

            if (window.tabStreamer && !window.tabStreamer.isStreaming()) {
              isStreamingSetup = true;
              const viewport = { width: window.innerWidth, height: window.innerHeight };
              const wsEndpoint = '${this.env.KAKU_WS_ENDPOINT}/v1/agents/connections/${linkId}';
              await window.tabStreamer.init(wsEndpoint, viewport, {
                mode: 'FULL_SCREEN',
                frameRate: 15,
                debug: true,
                enableInputDetection: false
              });

              await window.tabStreamer.start(viewport);

              // Signal connection agent for automatic WebRTC renegotiation after successful reconnection
              try {
                const signalSocket = new WebSocket(agentWsEndpoint);
                signalSocket.onopen = function() {
                  console.log('[kazeel] Signaling connection agent for automatic WebRTC renegotiation');
                  signalSocket.send(JSON.stringify({
                    type: 'social-login-reconnected',
                    timestamp: Date.now(),
                    viewport: viewport,
                    wsEndpoint: wsEndpoint
                  }));
                };
                signalSocket.onerror = function(error) {
                  console.warn('[kazeel] Failed to signal connection agent for renegotiation:', error);
                };
              } catch (error) {
                console.warn('[kazeel] Failed to create signal connection to agent:', error);
              }
            } else if (window.tabStreamer && window.tabStreamer.isStreaming()) {
              console.log('[kazeel] Tab streamer already streaming, no action needed');
            } else {
              console.warn('[kazeel] Tab streamer not available for setup');
            }
          } catch (error) {
            console.error('[kazeel] Failed to auto-initialize tab streamer:', error);
            isStreamingSetup = false; // Reset flag on error to allow retry
          }
        }
        setupTabStreaming();
      })();
    `;
  }

  /**
   * Set up WebRTC streaming for social login
   */
  private async setupSocialLoginStreaming(sessionId: string, linkId: string): Promise<void> {
    if (!this.cdp) {
      throw new Error('CDP client not initialized');
    }

    try {
      this.log('Setting up WebRTC streaming for social login');

      // Initialize tab streamer in the social login tab
      await this.cdp.Runtime.evaluate(
        {
          expression: `
            (async () => {
              try {
                if (window.tabStreamer) {
                  const viewport = { width: window.innerWidth, height: window.innerHeight };
                  const wsEndpoint = '${this.env.KAKU_WS_ENDPOINT}/v1/agents/connections/${linkId}';

                  console.log('[kazeel] Initializing social login tab streamer');

                  await window.tabStreamer.init(wsEndpoint, viewport, {
                    mode: 'FULL_SCREEN',
                    frameRate: 15,
                    debug: true,
                    enableInputDetection: false
                  });

                  await window.tabStreamer.start(viewport);
                  console.log('[kazeel] Social login tab streamer started successfully');
                } else {
                  console.warn('[kazeel] Tab streamer not available for immediate setup');
                  // The persistent script will handle initialization when available
                }
              } catch (error) {
                console.error('[kazeel] Error setting up social login tab streamer:', error);
              }
            })();
          `,
          awaitPromise: true,
        },
        sessionId,
      );

      this.log('WebRTC streaming setup completed for social login');
    } catch (error) {
      this.error('Failed to setup WebRTC streaming for social login:', error);
      throw error;
    }
  }

  async run(event: WorkflowEvent<ConnectionsWorkflowParams>, step: WorkflowStep) {
    this.connectionAgentStub = this.getConnectionAgentStub(event.payload.linkId);

    const browserManager = new BrowserManager({
      env: this.env,
      event: event.payload,
      llmService: this.llmService,
    });

    // Setup-Step 1: Create CDP client
    const { cdp, browserSession } = await browserManager.createCDPSession();

    this.cdp = cdp;
    this.browserSession = browserSession;

    const workflowStepManager = new WorkflowStepsManager({
      env: this.env,
      step: step,
      eventPayload: event.payload,
      errorHandler: async (_) => {
        await this.cleanupResources();
      },
    });

    // Setup-Step 2: Setup browser session
    const { targetId, controlTabTargetId } = await workflowStepManager.tryRunStep(
      WorkflowStepName.SETUP_BROWSER_SESSION,
      async () => {
        if (!this.cdp) throw new Error('CDP client not initialized');
        return await browserManager.setupBrowserSession(this.cdp);
      },
    );

    this.targetId = targetId;
    this.controlTabTargetId = controlTabTargetId;

    // Setup-Step 3: Handle browser two tab architecture
    const { targetSessionId, controlTabSessionId } = await browserManager.setupTwoTabArchitecture({
      cdp: this.cdp,
      controlTabTargetId: this.controlTabTargetId,
      targetId: this.targetId,
      browserSessionWSEndpoint: this.browserSession.wsEndpoint,
      wsUrl: `${this.env.KAKU_WS_ENDPOINT}/v1/agents/connections/${event.payload.linkId}`,
    });

    this.targetSessionId = targetSessionId;
    this.controlTabSessionId = controlTabSessionId;

    //Disable agent attachedToTarget listener to prevent changes from child targets
    this.connectionAgentStub!.disableAttachedToTargetListener();

    // Setup-Step 4: Handle browser two tab architecture
    await workflowStepManager.tryRunStep(
      WorkflowStepName.SETUP_VIEWPORT_AND_DEVICE_METRICS,
      async () => {
        if (!this.cdp || !this.targetSessionId) throw new Error('CDP client not initialized');
        await browserManager.ensureViewportSettings({
          cdp: this.cdp,
          targetSessionId: this.targetSessionId,
        });
      },
    );

    await setupInputFocusListener(this.cdp!, this.targetSessionId);
    browserManager.injectTargetTabScriptsForCaptcha({
      cdp: this.cdp!,
      targetSessionId: this.targetSessionId!,
      wsUrl: `${this.env.KAKU_WS_ENDPOINT}/v1/agents/connections/${event.payload.linkId}`,
      targetId: targetId,
    });

    await workflowStepManager.tryRunStep(WorkflowStepName.INITIALIZE_SESSION, async () => {
      if (!this.cdp || !this.targetSessionId || !this.controlTabSessionId)
        throw new Error('CDP client not initialized');
      await browserManager.navigateToLoginPage({
        cdp: this.cdp,
        targetSessionId: this.targetSessionId,
      });
    });

    const screenshot = await workflowStepManager.tryRunStep(
      WorkflowStepName.CAPTURE_SCREENSHOT,
      async () => {
        if (!this.cdp || !this.targetSessionId) throw new Error('CDP client not initialized');
        return await browserManager.captureScreenshot({
          cdp: this.cdp,
          targetSessionId: targetSessionId,
        });
      },
      defaultWorkflowRetryConfig('10 seconds'),
    );

    let isCompleted = false;
    let latestScreenshot = screenshot;
    let captchaAlreadySolvedCount = 0;

    while (!isCompleted) {
      // Step 3: Analyze page state with AI
      let pageStateResult = await workflowStepManager.tryRunStep(
        WorkflowStepName.ANALYZE_PAGE_STATE,
        async () => {
          return await this.analyzePageState(
            latestScreenshot,
            event.payload.platformId,
            event.payload.linkId,
          );
        },
        defaultWorkflowRetryConfig('2 minutes'),
      );

      const pageRequiresExternalFormSubmission = this.isMultiFactorPushApprovalScreen(
        pageStateResult.classificationResult,
      );

      const screenClass = pageStateResult.classificationResult.screenInfo.screenClass;

      // Handle different screen types with switch case
      switch (screenClass) {
        case 'loading-screen': {
          latestScreenshot = await this.handleLoadingScreen(
            workflowStepManager,
            browserManager,
            targetSessionId,
          );
          continue;
        }

        case 'captcha-screen': {
          const result = await this.handleCaptchaScreen(
            event,
            workflowStepManager,
            browserManager,
            pageStateResult,
            latestScreenshot,
            targetSessionId,
            captchaAlreadySolvedCount,
          );
          latestScreenshot = result.screenshot;
          captchaAlreadySolvedCount = result.captchaAlreadySolvedCount;
          continue;
        }

        case 'logged-in-screen':
        case 'profile-management-screen': {
          // Check if user is authenticated
          if (pageStateResult.classificationResult.screenInfo.authState === 'authenticated') {
            await this.handleAuthenticatedScreen(event, latestScreenshot);
            isCompleted = true;
            break;
          }
          // If not authenticated, fall through to default handling
        }

        case 'multi-factor-code-verification-screen':
        case 'multi-factor-push-approval-screen':
        case 'multi-factor-multiple-options-screen':
        case 'passkey-screen':
        case 'trust-device-screen':
        case 'other':
        default: {
          // Handle standard form interaction screens
          await this.handleStandardFormScreen(
            workflowStepManager,
            browserManager,
            pageStateResult,
            latestScreenshot,
            pageRequiresExternalFormSubmission,
            targetSessionId,
            event,
          );
          break;
        }
      }

      // Check if authenticated after screen handling
      if (
        pageStateResult.classificationResult.screenInfo.authState === 'authenticated' &&
        !isCompleted
      ) {
        await this.handleAuthenticatedScreen(event, latestScreenshot);
        isCompleted = true;
        break;
      }

      // Update latestScreenshot if returned from standard form screen handling
      if (!isCompleted) {
        latestScreenshot = await workflowStepManager.tryRunStep(
          WorkflowStepName.TAKE_SCREENSHOT,
          async () => {
            if (!this.cdp || !this.targetSessionId) throw new Error('CDP client not initialized');
            return await browserManager.captureScreenshot({
              cdp: this.cdp,
              targetSessionId: targetSessionId,
            });
          },
          defaultWorkflowRetryConfig('10 seconds'),
        );
      }
    }
  }

  private async analyzePageState(
    screenshot: string,
    platformId: PlatformTypes,
    linkId: string,
  ): Promise<PageStateResult> {
    const layoutMetrics = await this.cdp!.Page.getLayoutMetrics(undefined, this.targetSessionId);
    const viewPort = {
      width: layoutMetrics.cssLayoutViewport.clientWidth,
      height: layoutMetrics.cssLayoutViewport.clientHeight,
    };

    const version = getPlatformVersion(platformId, this.env);

    const result = await makeParallelLLMCalls(
      this.llmService,
      {
        linkId: linkId,
        platform: platformId,
        screenshot,
        skipCache: this.env.SKIP_CACHE,
        viewportWidth: viewPort.width,
        viewportHeight: viewPort.height,
        version,
      },
      FORM_VISION_PROMPT_V6,
      FORM_VISION_CLASSIFICATION_PROMPT_V34,
    );
    this.log('Parallel LLM calls completed');

    return result;
  }

  private async notifyConnectionAgent(
    formData: PageStateResult,
    screenshot: string,
  ): Promise<void> {
    if (!this.connectionAgentStub) {
      throw new Error('Connection agent stub is not initialized');
    }

    await this.connectionAgentStub.onFormStateChange(screenshot, {
      ...formData,
    });
  }

  private isMultiFactorPushApprovalScreen(classificationResult: ClassificationResult): boolean {
    return classificationResult.screenInfo.screenClass === 'multi-factor-push-approval-screen';
  }

  private async cleanupResources(): Promise<void> {
    if (this.browserSession?.sessionId) {
      await this.browserService.closeSession(this.browserSession.sessionId);
    } else {
      this.warn('No browser session to close');
    }
  }

  private getConnectionAgentStub(linkId: string): DurableObjectStub<Connections> {
    const agent = this.env.Connections.idFromName(linkId);
    return this.env.Connections.get(agent);
  }

  private listenForSocialLoginCompletion(platform: PlatformTypes, target: PageAttachmentType) {
    if (!this.cdp) {
      throw new Error('CDP client not initialized');
    }

    switch (target.type) {
      case 'new-target':
        const socialLoginFrameClosedListener = (event: any) => {
          const eventTargetId: string = event.params.targetId;
          if (eventTargetId == target.targetId) {
            //Social login completed!!
            this.connectionAgentStub!.sendSocialLoginCompletedEvent();
            this.cdp!.Page.removeEventListener(
              'detachedFromTarget',
              socialLoginFrameClosedListener,
            );
          } else {
            this.warn(
              `Another frame was removed. Ours: ${target.targetId}, closed: ${eventTargetId}`,
            );
          }
        };
        this.cdp.Target.addEventListener('detachedFromTarget', socialLoginFrameClosedListener);
        break;

      case 'new-frame':
        const onFrameNavigated = (event: any) => {
          const newUrl = event.params.frame.url;
          if (newUrl.includes(platform)) {
            //Social login completed!!
            this.connectionAgentStub!.sendSocialLoginCompletedEvent();
            this.cdp!.Page.removeEventListener('frameNavigated', onFrameNavigated);
          }
        };

        this.cdp.Page.addEventListener('frameNavigated', onFrameNavigated);
        break;
    }
  }

  private async finishTabStreamingAndResumeWithKazeelForms() {
    if (this.connectionAgentStub) {
      await this.connectionAgentStub.disableSocialLoginUIState();
    }
  }
  private async executeMainAction(
    pageStateResult: PageStateResult,
    browserManager: BrowserManager,
  ) {
    if (!this.cdp || !this.targetSessionId || !this.executionContextId)
      throw new Error('Browser initialization not setup properly');

    const actions = generateActionsFromExtractionResult(pageStateResult.extractionResult);
    const mainAction = getMainPrimaryAction(actions);
    if (!mainAction) {
      this.warn(
        `No main action found to execute after captcha solution. ${JSON.stringify(actions)}`,
      );
      return;
    }

    const elementCoordinateMapping =
      await this.connectionAgentStub!.getResolvedElementCoordinateMapping();

    if (!elementCoordinateMapping) {
      this.warn(
        `Could not resolve element coordinates for main action. ${JSON.stringify(elementCoordinateMapping)}`,
      );
      return;
    }

    const coordinates = elementCoordinateMapping[mainAction.name];

    if (!coordinates) {
      this.warn(`No coordinates found for main action: ${mainAction.name}`);
      return;
    }

    const actionWithCoordinates = { ...mainAction, coordinates };

    await browserManager.executeAction({
      cdp: this.cdp,
      targetSessionId: this.targetSessionId,
      action: actionWithCoordinates,
    });
  }

  /**
   * Handles loading screen state - waits for page to complete loading
   */
  private async handleLoadingScreen(
    workflowStepManager: any,
    browserManager: BrowserManager,
    targetSessionId: string,
  ): Promise<string> {
    this.log('→ Page is loading, waiting for completion and capturing new screenshot');

    const screenshot = await workflowStepManager.tryRunStep(
      WorkflowStepName.SCREENSHOT_AFTER_LOADING,
      async () => {
        if (!this.cdp || !this.targetSessionId) throw new Error('CDP client not initialized');
        return await browserManager.captureScreenshot({
          cdp: this.cdp,
          targetSessionId: targetSessionId,
        });
      },
      defaultWorkflowRetryConfig('10 seconds'),
    );

    this.log('✓ Page loading completed, screenshot updated');
    return screenshot;
  }

  /**
   * Handles captcha screen state - sets up captcha detection and waits for resolution
   */
  private async handleCaptchaScreen(
    event: WorkflowEvent<ConnectionsWorkflowParams>,
    workflowStepManager: any,
    browserManager: BrowserManager,
    pageStateResult: PageStateResult,
    latestScreenshot: string,
    targetSessionId: string,
    captchaAlreadySolvedCount: number,
  ): Promise<{ screenshot: string; captchaAlreadySolvedCount: number }> {
    // Save the screenshot to R2
    storeConnectionScreenshotToR2(
      this.env.SCREENSHOTS_INBOUND_BUCKET,
      event.payload.userId,
      event.payload.platformId,
      event.payload.sessionId,
      ConnectionWorkflowState.OnCaptcha,
      latestScreenshot,
    );

    await workflowStepManager.tryRunStep(WorkflowStepName.INJECT_SCRIPT, async () => {
      if (!this.cdp || !this.targetSessionId || !this.executionContextId || !this.targetId)
        throw new Error('Browser initialization not setup properly');

      await browserManager.injectTargetTabScriptsForCaptcha({
        cdp: this.cdp,
        targetSessionId: this.targetSessionId,
        wsUrl: `${this.env.KAKU_WS_ENDPOINT}/v1/agents/connections/${event.payload.linkId}`,
        targetId: this.targetId,
      });
    });
    const viewport = { width: K_CUSTOM_VIEWPORT.width, height: K_CUSTOM_VIEWPORT.height };

    await workflowStepManager.tryRunStep(
      WorkflowStepName.SEND_CAPTCHA_DETECTION_RESPONSE,
      async () => {
        if (!this.cdp || !this.connectionAgentStub) throw new Error('CDP client not initialized');

        await this.connectionAgentStub.handleCaptchaDetected(this.executionContextId!, viewport);
        await initRTCSteaming(
          { cdpSession: this.cdp },
          this.executionContextId!,
          this.targetSessionId!,
          viewport,
        );

        this.connectionAgentStub.startCoordinatesResolution(
          latestScreenshot,
          pageStateResult.extractionResult,
        );
      },
      defaultWorkflowRetryConfig('1 minutes'),
    );

    this.log('→ Waiting for captcha solved notification');
    const captchaSolvedEvent = await workflowStepManager.waitForCaptchaSolvedEvent();

    if (captchaSolvedEvent.payload.source === 'manual_verification') {
      await this.executeMainAction(pageStateResult, browserManager);
    }

    if (captchaSolvedEvent.payload.source === 'tf_initial_resolved') {
      this.log('→ Captcha already solved, waiting for next captcha');
      await workflowStepManager.tryRunStep(
        WorkflowStepName.COUNT_ALREADY_SOLVED_CAPTCHA,
        async () => {
          captchaAlreadySolvedCount++;
          if (captchaAlreadySolvedCount > 3) {
            throw new Error('Too many already solved captchas detected by TensorFlow Model');
          }
          // delay to wait for captcha to fully load before retrying
          await sleep(1000);
        },
      );
    }

    this.log('✓ Received captcha solved event', {
      differencePercentage: captchaSolvedEvent.payload.differencePercentage,
    });

    if (!this.cdp || !this.targetSessionId) {
      throw new Error('CDP client not initialized');
    }

    await workflowStepManager.tryRunStep(
      WorkflowStepName.WAIT_FOR_PAGE_UPDATE_AFTER_CAPTCHA_COMPLETION,
      async () => {
        if (!this.cdp) throw new Error('CDP client not initialized');

        const configSettings: PlatformDetectionConfig =
          platformDetectionConfigs[event.payload.platformId];

        return await browserManager.waitForPageUpdateAfterSubmission({
          cdp: this.cdp,
          currentFormVisionResult: pageStateResult.extractionResult,
          targetSessionId: targetSessionId,
          configSettings: configSettings,
        });
      },
    );

    const screenshot = await browserManager.captureScreenshot({
      cdp: this.cdp,
      targetSessionId: targetSessionId,
    });

    return { screenshot, captchaAlreadySolvedCount };
  }

  /**
   * Handles authenticated screen state - completes the workflow
   */
  private async handleAuthenticatedScreen(
    event: WorkflowEvent<ConnectionsWorkflowParams>,
    latestScreenshot: string,
  ): Promise<void> {
    this.log('✓ Workflow completed: user authenticated');

    if (this.cdp) {
      const browserDataAdapter = new CDPBrowserDataAdapter(this.cdp, this.targetSessionId);
      await this.browserStateService.updateBrowserState(
        browserDataAdapter,
        event.payload.userId,
        event.payload.platformId,
      );
    }
    await this.connectionAgentStub?.onAuthenticated(event.payload.linkId, event.payload.userId);

    // Save the screenshot to R2
    storeConnectionScreenshotToR2(
      this.env.SCREENSHOTS_INBOUND_BUCKET,
      event.payload.userId,
      event.payload.platformId,
      event.payload.sessionId,
      ConnectionWorkflowState.Authenticated,
      latestScreenshot,
    );

    await this.cleanupResources();
  }

  /**
   * Handles standard form interaction screens (login, multi-factor, etc.)
   */
  private async handleStandardFormScreen(
    workflowStepManager: WorkflowStepsManager,
    browserManager: BrowserManager,
    pageStateResult: PageStateResult,
    latestScreenshot: string,
    pageRequiresExternalFormSubmission: boolean,
    targetSessionId: string,
    event: WorkflowEvent<ConnectionsWorkflowParams>,
  ): Promise<void> {
    // Step 4: Notify connection agent of page state (Phase 1 + start Phase 2)
    await workflowStepManager.tryRunStep(
      WorkflowStepName.NOTIFY_CONNECTION_AGENT,
      async () => {
        await this.notifyConnectionAgent(pageStateResult, latestScreenshot);
      },
      defaultWorkflowRetryConfig('30 seconds'),
    );

    if (pageRequiresExternalFormSubmission) {
      // We start listening for when a user will authenticate using external methods
      browserManager
        .waitForPageUpdateAfterSubmission({
          cdp: this.cdp!,
          currentFormVisionResult: pageStateResult.extractionResult,
          targetSessionId: targetSessionId,
          configSettings: twoFactorChangeDetectionConfig,
        })
        .then(async (pageChangeTypeResponse) => {
          if (pageChangeTypeResponse != null) {
            this.log(`Detected 2-factor state change by type ${pageChangeTypeResponse}`);
            // There was a state change from external source, send a form-submission to let the flow resume
            this.connectionAgentStub!.sendTwoFactorAuthenticationCompletedEvent();
          }
        });
    }

    // Step 5: Wait for user form input
    const formSubmissionEvent = await workflowStepManager.waitForFormSubmittedEvent();

    if (formSubmissionEvent.payload.source == FormSubmissionPayloadSource.PAGE_FORM_SUBMISSION) {
      this.log('Received Form submission event from user filling the form');
      if (pageRequiresExternalFormSubmission) {
        // We need to cancel the listeners for external form submission
        browserManager.cancelActiveBrowserStateChangeListeners();
      }

      // Step 6: Execute form actions
      await this.executeFormActions(
        workflowStepManager,
        browserManager,
        pageStateResult,
        formSubmissionEvent,
        targetSessionId,
      );

      // Step 7: Wait for page update after submission
      await this.waitForPageUpdateAfterSubmission(
        workflowStepManager,
        browserManager,
        pageStateResult,
        targetSessionId,
        event,
      );
    } else if (
      formSubmissionEvent.payload.source ==
      FormSubmissionPayloadSource.TWO_FACTOR_AUTHENTICATION_COMPLETION
    ) {
      this.log(
        `Change detected from user acknowledging from external source, skipping form actions step`,
      );
      await this.connectionAgentStub!.markWaitingForAgent();
    }

    // Take final screenshot
    const screenshot = await workflowStepManager.tryRunStep(
      WorkflowStepName.TAKE_SCREENSHOT,
      async () => {
        if (!this.cdp || !this.targetSessionId) throw new Error('CDP client not initialized');
        return await browserManager.captureScreenshot({
          cdp: this.cdp,
          targetSessionId: targetSessionId,
        });
      },
      defaultWorkflowRetryConfig('10 seconds'),
    );

    // Save the screenshot to R2
    storeConnectionScreenshotToR2(
      this.env.SCREENSHOTS_INBOUND_BUCKET,
      event.payload.userId,
      event.payload.platformId,
      event.payload.sessionId,
      ConnectionWorkflowState.UserFormFilled,
      screenshot,
    );
  }

  /**
   * Executes form actions based on user input
   */
  private async executeFormActions(
    workflowStepManager: any,
    browserManager: BrowserManager,
    pageStateResult: PageStateResult,
    formSubmissionEvent: any,
    targetSessionId: string,
  ): Promise<void> {
    await workflowStepManager.tryRunStep(
      WorkflowStepName.EXECUTE_FORM_ACTIONS,
      async () => {
        if (!this.cdp || !this.targetSessionId) throw new Error('CDP client not initialized');

        const aiActionsFields = pageStateResult.extractionResult.includedControls.fields.filter(
          (item: any) => item.isDontAskAgainControl,
        );
        const key = this.env.PAYLOAD_ENCRYPTION_KEY;
        const encryptionKey = await getEncryptionKey(key);
        const decrypted = await decryptData(formSubmissionEvent.payload.payload, encryptionKey);
        const actionsPayload = JSON.parse(decrypted as string) as FormSubmissionEventPayload;

        const nonAIActions = actionsPayload.actions.filter(
          (action: any) => !aiActionsFields.some((field: any) => field.id === action.name),
        );

        if (aiActionsFields.length > 0) {
          await browserManager.executeAIActions(
            this.cdp,
            this.targetSessionId,
            aiActionsFields,
            formSubmissionEvent.payload.coordinates,
          );
          await sleep(100);
        }
        if (nonAIActions.length > 0) {
          await browserManager.executeFormActions(this.cdp, this.targetSessionId, nonAIActions);
        }
      },
      defaultWorkflowRetryConfig('6 seconds', 1),
    );
  }

  /**
   * Waits for page update after form submission
   */
  private async waitForPageUpdateAfterSubmission(
    workflowStepManager: any,
    browserManager: BrowserManager,
    pageStateResult: PageStateResult,
    targetSessionId: string,
    event: WorkflowEvent<ConnectionsWorkflowParams>,
  ): Promise<void> {
    await workflowStepManager.tryRunStep(
      WorkflowStepName.WAIT_FOR_PAGE_UPDATE_AFTER_SUBMISSION,
      async () => {
        if (!this.cdp) throw new Error('CDP client not initialized');

        const configSettings: PlatformDetectionConfig =
          platformDetectionConfigs[event.payload.platformId];

        return await browserManager.waitForPageUpdateAfterSubmission({
          cdp: this.cdp,
          currentFormVisionResult: pageStateResult.extractionResult,
          targetSessionId: targetSessionId,
          configSettings: configSettings,
        });
      },
    );
  }
}
