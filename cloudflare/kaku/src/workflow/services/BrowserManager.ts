import { ElementCoordinateMapping } from '../../agent/services/coordinate-resolution';
import { Action, ExtractionResult, FormField } from '../../agent/types/llm-result';
import {
  getHashedScriptUrl,
  initScreenCropper,
  initTensorFlowDetector,
  injectScript,
  injectTensorFlowJS,
  wrapForMainFrameOnly,
} from '../../browser';
import { withCdp } from '../../browser/client-api';
import { CDP } from '../../browser/simple-cdp';
import { ErrorService } from '../../common/error';
import { LLMService } from '../../llm/LLMService';
import { platformDetails } from '../../ui/constants';
import {
  BrowserDataAdapter,
  BrowserSession,
  RemoteBrowserService,
} from '../adapters/BrowserDataAdapter';
import { CDPBrowserDataAdapter } from '../adapters/CDPBrowserDataAdapter';
import { BrowserStateService } from '../BrowserStateService';
import { CoordinatorDOBrowserStateRepository } from '../CoordinatorDOBrowserStateRepository';
import { ConnectionsWorkflowParams } from '../types';
import { StateChangeType } from '../types/StateChangeType';
import {
  getPlatformVersion,
  K_CUSTOM_VIEWPORT,
  pageStateResultComparisonPromptInstructions,
  PlatformDetectionConfig,
} from '../utils/constants';
import { sleep, withTimeout } from '../utils/helpers';
import { BrowserServiceFactory } from './BrowserServiceFactory';

export class BrowserManager {
  // Logging configuration
  private config = {
    debug: true, // Set to true for verbose logging
  };

  private readonly env: Env;
  private readonly eventPayload: ConnectionsWorkflowParams;
  private readonly llmService: LLMService;

  private browserService: RemoteBrowserService;
  private browserStateService: BrowserStateService;
  private browserDataAdapter?: BrowserDataAdapter;

  private stateChangeAbortController = new AbortController();

  constructor({
    env,
    event,
    llmService,
  }: {
    env: Env;
    event: ConnectionsWorkflowParams;
    llmService: LLMService;
  }) {
    this.eventPayload = event;
    this.env = env;
    this.llmService = llmService;
    this.browserService = BrowserServiceFactory.createFromEnvironment(env);
    this.browserStateService = new BrowserStateService(
      new CoordinatorDOBrowserStateRepository(this.env.CoordinatorDO),
    );
  }

  private log(...args: any[]): void {
    if (this.config.debug) {
      console.log('[kazeel][browser-manager]', ...args);
    }
  }

  private warn(...args: any[]): void {
    console.warn('[kazeel][browser-manager]', ...args);
  }

  private error(...args: any[]): void {
    console.error('[kazeel][browser-manager]', ...args);
  }

  async createCDPSession(): Promise<{ cdp: CDP; browserSession: BrowserSession }> {
    try {
      const browserSession = await this.browserService.getSession(this.eventPayload.sessionId);

      const cdp = new CDP({ webSocketDebuggerUrl: browserSession.wsEndpoint });

      await cdp.Target.setAutoAttach({
        autoAttach: true,
        flatten: true,
        waitForDebuggerOnStart: false,
      });

      return { cdp, browserSession };
    } catch (error) {
      await this.handleBrowserError(error);
      throw error;
    }
  }

  async setupBrowserSession(cdp: CDP) {
    const controlTabTarget = await cdp.Target.createTarget({ url: 'about:blank' });
    const mainTargetResponse = await cdp.Target.createTarget({ url: 'about:blank' });

    return {
      targetId: mainTargetResponse.targetId,
      controlTabTargetId: controlTabTarget.targetId,
    };
  }

  async setupTwoTabArchitecture({
    cdp,
    targetId,
    controlTabTargetId,
    browserSessionWSEndpoint,
    wsUrl,
  }: {
    cdp: CDP;
    targetId: string;
    controlTabTargetId: string;
    browserSessionWSEndpoint: string;
    wsUrl: string;
  }) {
    try {
      const sessionIds = await this.attachControlAndTargetTabs({
        cdp: cdp,
        targetId: targetId,
        controlTabTargetId: controlTabTargetId,
      });

      await this.injectInitialControlTabScripts({
        cdp: cdp,
        targetId: targetId,
        controlTabSessionId: sessionIds.controlTabSessionId,
        browserSessionWSEndpoint: browserSessionWSEndpoint,
        signalingServerUrl: wsUrl,
      });

      //Load browser state, if available
      if (!this.browserDataAdapter) {
        this.browserDataAdapter = new CDPBrowserDataAdapter(cdp, sessionIds.targetSessionId);
      }

      await this.browserStateService.loadBrowserStateToPage(
        this.browserDataAdapter,
        this.eventPayload.userId,
        this.eventPayload.platformId,
      );

      return sessionIds;
    } catch (error) {
      this.log(`An error occurred ${JSON.stringify(error)}`);
      await this.handleBrowserError(error);
      throw error;
    }
  }

  /**
   * Ensures the viewport and device metrics are set to the correct dimensions
   * This should be called after navigation or any time we need to guarantee viewport consistency
   */
  async ensureViewportSettings({
    cdp,
    targetSessionId,
  }: {
    cdp: CDP;
    targetSessionId: string;
  }): Promise<void> {
    await cdp.Emulation.setDeviceMetricsOverride(
      {
        //store these to constants
        width: K_CUSTOM_VIEWPORT.width,
        height: K_CUSTOM_VIEWPORT.height,
        deviceScaleFactor: 1,
        mobile: false,
      },
      targetSessionId,
    );
  }

  async navigateToLoginPage({
    cdp,
    targetSessionId,
  }: {
    cdp: CDP;
    targetSessionId: string;
  }): Promise<void> {
    const pageLoad = this.waitForPageLoad(cdp);

    await cdp.Page.navigate(
      {
        url: platformDetails[this.eventPayload.platformId].loginLink,
      },
      targetSessionId,
    );

    await pageLoad;
  }

  async setupExecutionContextListener({
    cdp,
    targetId,
  }: {
    cdp: CDP;
    targetId: string;
  }): Promise<number> {
    // remove promise. I just want to update the executioncontextId
    return await new Promise((resolve) => {
      cdp.Runtime.addEventListener('executionContextCreated', (fullData: { params: any }) => {
        const { context } = fullData.params;
        if (context.name === 'kaku-target-world' && context.auxData.frameId === targetId) {
          cdp?.Runtime.removeEventListener('executionContextCreated', (e) => {});
          resolve(context.id);
        }
      });
    });
  }

  /**
   * Inject target tab scripts for captcha handling
   * Control tab scripts are already injected early, so only inject target tab scripts
   */
  async injectTargetTabScriptsForCaptcha({
    cdp,
    targetSessionId,
    targetId,
    wsUrl,
  }: {
    cdp: CDP;
    targetSessionId: string;
    targetId: string;
    wsUrl: string;
  }) {
    // Inject target tab scripts only (control tab scripts already injected early)
    await this.injectTargetTabScripts({
      cdp,
      targetSessionId,
      signalingServerUrl: wsUrl,
      targetTabId: targetId,
    });
  }

  async executeFormActions(cdp: CDP, targetSessionId: string, actions: Action[]): Promise<void> {
    try {
      // Sort actions by order, ensuring submit actions are last
      const sortedActions = actions.sort((a, b) => a.order - b.order);

      // Process each action in order
      for (const action of sortedActions) {
        await this.executeAction({ cdp: cdp, targetSessionId: targetSessionId, action: action });
      }

      this.log('✓ Form actions executed successfully');
    } catch (error) {
      this.error(`✖ Error executing form actions: ${(error as Error).message}`);
      throw error;
    }
  }

  async executeAIActions(
    cdp: CDP,
    targetSessionId: string,
    aiActionsFields: FormField[],
    elementCoordinateMapping?: ElementCoordinateMapping,
  ): Promise<void> {
    try {
      if (aiActionsFields.length === 0) {
        this.log('No AI actions to execute');
        return;
      }

      aiActionsFields.map(async (aiActionField) => {
        await this.handleKeepMeLoggedIn(
          cdp,
          targetSessionId,
          aiActionField,
          elementCoordinateMapping,
        );
        return;
      });
    } catch (error) {
      this.error(`Error executing AI form actions: ${(error as Error).message}`);
      throw error;
    }
  }

  private async handleKeepMeLoggedIn(
    cdp: CDP,
    targetSessionId: string,
    action: FormField,
    elementCoordinateMapping?: ElementCoordinateMapping,
  ): Promise<void> {
    if (action.checked) {
      // early exit, no need to click as the checkbox is already checked
      return;
    }
    const coordinates = elementCoordinateMapping?.[action.id];

    if (!coordinates) {
      throw new Error('No coordinates found for keep-me-logged-in action');
    }

    await this.clickAt({ cdp: cdp, targetSessionId: targetSessionId, coordinates: coordinates });
  }

  async executeAction({
    cdp,
    targetSessionId,
    action,
  }: {
    cdp: CDP;
    targetSessionId: string;
    action: Action;
  }): Promise<void> {
    const { type, coordinates, name, value } = action;

    if (type === 'fill' && value) {
      await this.fillTextField({
        cdp: cdp,
        targetSessionId: targetSessionId,
        coordinates: coordinates,
        name: name,
        value: value,
      });
    } else if (type === 'click' || type === 'select') {
      await this.clickAt({ cdp: cdp, targetSessionId: targetSessionId, coordinates: coordinates });
    }
  }

  private async fillTextField({
    cdp,
    targetSessionId,
    coordinates,
    name,
    value,
  }: {
    cdp: CDP;
    targetSessionId: string;
    coordinates: { x: number; y: number };
    name: string;
    value: string;
  }): Promise<void> {
    try {
      // Click on the field
      await this.clickAt({ cdp: cdp, targetSessionId: targetSessionId, coordinates: coordinates });

      // Triple-click to select all text
      await this.tripleClickAt({
        cdp: cdp,
        targetSessionId: targetSessionId,
        coordinates: coordinates,
      });

      // Clear any existing text
      await this.pressBackspace({ cdp: cdp, targetSessionId: targetSessionId });

      // Type the value
      await this.typeText({ cdp: cdp, targetSessionId: targetSessionId, text: value });
    } catch (error) {
      throw Error(`Error clicking: ${(error as Error).message}`);
    }
  }

  private async clickAt({
    cdp,
    targetSessionId,
    coordinates,
  }: {
    cdp: CDP;
    targetSessionId: string;
    coordinates: { x: number; y: number };
  }): Promise<void> {
    try {
      // Mouse down
      await cdp.Input.dispatchMouseEvent(
        {
          type: 'mousePressed',
          x: coordinates.x,
          y: coordinates.y,
          button: 'left',
          clickCount: 1,
        },
        targetSessionId,
      );

      // Mouse up to complete click
      await cdp.Input.dispatchMouseEvent(
        {
          type: 'mouseReleased',
          x: coordinates.x,
          y: coordinates.y,
          button: 'left',
          clickCount: 1,
        },
        targetSessionId,
      );
    } catch (error) {
      throw Error(`Error clicking: ${(error as Error).message}`);
    }
  }

  private async tripleClickAt({
    cdp,
    targetSessionId,
    coordinates,
  }: {
    cdp: CDP;
    targetSessionId: string;
    coordinates: { x: number; y: number };
  }): Promise<void> {
    for (let i = 0; i < 3; i++) {
      await cdp.Input.dispatchMouseEvent(
        {
          type: 'mousePressed',
          x: coordinates.x,
          y: coordinates.y,
          button: 'left',
          clickCount: i + 1,
        },
        targetSessionId,
      );
      await cdp.Input.dispatchMouseEvent(
        {
          type: 'mouseReleased',
          x: coordinates.x,
          y: coordinates.y,
          button: 'left',
          clickCount: i + 1,
        },
        targetSessionId,
      );
    }
  }

  private async pressBackspace({
    cdp,
    targetSessionId,
  }: {
    cdp: CDP;
    targetSessionId: string;
  }): Promise<void> {
    await cdp.Input.dispatchKeyEvent(
      {
        type: 'keyDown',
        windowsVirtualKeyCode: 8,
        key: 'Backspace',
      },
      targetSessionId,
    );
    await cdp.Input.dispatchKeyEvent(
      {
        type: 'keyUp',
        key: 'Backspace',
      },
      targetSessionId,
    );
  }

  private async typeText({
    cdp,
    targetSessionId,
    text,
  }: {
    cdp: CDP;
    targetSessionId: string;
    text: string;
  }): Promise<void> {
    try {
      // Set the clipboard content
      await cdp.Input.insertText(
        {
          text: text,
        },
        targetSessionId,
      );

      this.log(
        `✓ Successfully pasted text: "${text.substring(0, 2)}${text.length > 4 ? '...' : ''}"`,
      );
    } catch (error) {
      throw Error(`✖ Error pasting text: ${(error as Error).message}`);
    }
  }

  cancelActiveBrowserStateChangeListeners() {
    this.stateChangeAbortController.abort();
  }

  async waitForPageLoad(cdp: CDP): Promise<boolean | null> {
    return await new Promise<boolean | null>((resolve) => {
      const handler = () => {
        this.log('Page Loaded event fired', { pageLoaded: true });
        resolve(true);
      };

      cdp?.Page.addEventListener('loadEventFired', handler);

      this.stateChangeAbortController.signal.addEventListener('abort', () => {
        this.log('Page load State change listener cancelled by abort controller');
        cdp?.Page.removeEventListener('loadEventFired', handler);
        resolve(null);
      });
    });
  }

  async waitForPageUpdateAfterSubmission({
    cdp,
    currentFormVisionResult,
    targetSessionId,
    configSettings,
  }: {
    cdp: CDP;
    currentFormVisionResult: ExtractionResult;
    targetSessionId: string;
    configSettings: PlatformDetectionConfig;
  }): Promise<StateChangeType | null> {
    // Strategy 1: Traditional page load (for legacy platforms)
    const pageLoad = this.waitForPageLoad(cdp).then((result) => {
      if (result === true) {
        this.log('✓ Page load event fired - cancelling visual change detection');
        this.stateChangeAbortController.abort();
        return StateChangeType.PAGE_LOAD;
      }
    });

    // Strategy 2: Platform-aware visual change detection (for SPAs)
    const llmDetection = this.compareVisualChangesUsingLLM({
      cdp: cdp,
      targetSessionId: targetSessionId,
      config: configSettings,
      currentFormVisionResult: currentFormVisionResult,
    }).then((result) => {
      if (result === true) {
        this.stateChangeAbortController.abort();
        return StateChangeType.LLM_DETECTION;
      }
    });

    // Race all strategies with platform-specific timeout
    const result = await withTimeout(
      Promise.race([pageLoad, llmDetection /*, visualChanges*/]),
      configSettings.maxWaitTime,
    );

    return typeof result === 'string' ? (result as StateChangeType) : null;
  }

  private async compareVisualChangesUsingLLM({
    cdp,
    targetSessionId,
    config,
    currentFormVisionResult,
  }: {
    cdp: CDP;
    targetSessionId: string;
    config: PlatformDetectionConfig;
    currentFormVisionResult: ExtractionResult;
  }): Promise<boolean | null> {
    const layoutMetrics = await cdp.Page.getLayoutMetrics(undefined, targetSessionId);
    const viewPort = {
      width: layoutMetrics.cssLayoutViewport.clientWidth,
      height: layoutMetrics.cssLayoutViewport.clientHeight,
    };

    const version = getPlatformVersion(this.eventPayload.platformId, this.env);

    const startTime = Date.now();
    const { checkInterval, maxWaitTime, minChangeDetectionDelay = 0 } = config;

    // Wait for minimum delay before starting to check for LLM changes
    await sleep(minChangeDetectionDelay);

    return new Promise<boolean | null>(async (resolve) => {
      let resultFound = false;
      this.stateChangeAbortController.signal.addEventListener('abort', () => {
        this.log('LLM State change listener cancelled by abort controller');
        resultFound = true;
        resolve(null);
      });

      let storedScreenshot: string | null = null;

      while (!resultFound) {
        if (Date.now() - startTime > maxWaitTime) {
          this.log('→ LLM change detection timeout reached', resultFound);
          resultFound = true;
          resolve(null);
        }

        const currentScreenshot = await this.captureScreenshotForComparison({
          cdp: cdp,
          targetSessionId: targetSessionId,
        });

        if (storedScreenshot !== currentScreenshot && currentScreenshot != null) {
          const comparisonResult = await this.llmService.detectStateChangeFromUserAgentState({
            linkId: this.eventPayload.linkId,
            platform: this.eventPayload.platformId,
            prompt: pageStateResultComparisonPromptInstructions,
            agentVisionResultState: currentFormVisionResult,
            screenshot: currentScreenshot,
            skipCache: this.env.SKIP_CACHE,
            viewportWidth: viewPort.width,
            viewportHeight: viewPort.height,
            version: version,
          });
          storedScreenshot = currentScreenshot;

          if (comparisonResult) {
            resultFound = true;
            resolve(true);
          }
        }

        await sleep(checkInterval);
      }

      resolve(null);
    });
  }

  async captureScreenshot({
    cdp,
    targetSessionId,
  }: {
    cdp: CDP;
    targetSessionId: string;
  }): Promise<string> {
    this.log('📷 Taking standard screenshot');
    const screenshot = await cdp.Page.captureScreenshot(
      {
        format: 'png',
        captureBeyondViewport: false,
      },
      targetSessionId,
    );

    return screenshot.data;
  }

  /**
   * Captures a screenshot optimized for comparison
   * Returns base64 PNG string for direct pixelmatch processing
   */
  private async captureScreenshotForComparison({
    cdp,
    targetSessionId,
  }: {
    cdp: CDP;
    targetSessionId: string;
  }): Promise<string | null> {
    const screenshot = await cdp.Page.captureScreenshot(
      {
        format: 'png',
        captureBeyondViewport: false,
      },
      targetSessionId,
    );

    return screenshot.data;
  }

  /**
   * Inject scripts for the target tab (user interaction)
   */
  private async injectTargetTabScripts({
    cdp,
    targetSessionId,
    signalingServerUrl,
    targetTabId,
  }: {
    cdp: CDP;
    targetSessionId: string;
    signalingServerUrl: string;
    targetTabId: string;
  }) {
    const targetTabScript = getHashedScriptUrl(
      this.env.KAKU_API_ENDPOINT,
      'tab-streamer-bundle.min.js',
    );
    const scriptContent = await fetch(targetTabScript).then((r) => r.text());
    const updatedTargetScript = scriptContent
      .replace('${KAKU_WS_ENDPOINT}', signalingServerUrl)
      .replace('${TAB_ID}', targetTabId)
      .replace('${AUTO_INITIALIZE}', 'true');
    //Inject on current page
    await this.injectScriptWithEvaluate({
      cdp,
      sessionId: targetSessionId,
      script: updatedTargetScript,
    });
    //Inject for new document loads
    await this.injectScriptOnNewDocument({
      cdp: cdp,
      script: updatedTargetScript,
      sessionId: targetSessionId,
    });
  }

  private async attachControlAndTargetTabs({
    cdp,
    targetId,
    controlTabTargetId,
  }: {
    cdp: CDP;
    targetId: string;
    controlTabTargetId: string;
  }) {
    //Handle attaching to control and target tabs
    const attachToControlTargetResponse = await cdp.Target.attachToTarget({
      targetId: controlTabTargetId,
      flatten: true,
    });

    const attachToMainTargetResponse = await cdp.Target.attachToTarget({
      targetId: targetId,
      flatten: true,
    });

    // Enable required domains for both tabs
    this.log('About to enable requirements');
    await this.enableDomainsForBothTabs({
      cdp: cdp,
      targetSessionId: attachToMainTargetResponse.sessionId,
      controlTabSessionId: attachToControlTargetResponse.sessionId,
    });

    return {
      targetSessionId: attachToMainTargetResponse.sessionId,
      controlTabSessionId: attachToControlTargetResponse.sessionId,
    };
  }

  /**
   * Inject all required control tab scripts early using evaluate
   */
  private async injectInitialControlTabScripts({
    cdp,
    targetId,
    controlTabSessionId,
    browserSessionWSEndpoint,
    signalingServerUrl,
  }: {
    cdp: CDP;
    targetId: string;
    controlTabSessionId: string;
    browserSessionWSEndpoint: string;
    signalingServerUrl: string;
  }): Promise<void> {
    const baseBundle = getHashedScriptUrl(this.env.KAKU_API_ENDPOINT, 'base-bundle.min.js');
    try {
      // Fetch script contents for injection
      const [baseBundleScript] = await Promise.all([fetch(baseBundle).then((r) => r.text())]);

      const controlTabScript = baseBundleScript.replace('${KAKU_WS_ENDPOINT}', signalingServerUrl);
      const _controlTabScript = controlTabScript.replace(
        '${BROWSER_WS_ENDPOINT}',
        browserSessionWSEndpoint,
      );

      // Inject the prepared scripts on control tab
      await Promise.all([
        this.injectScriptWithEvaluate({
          cdp: cdp,
          sessionId: controlTabSessionId,
          script: _controlTabScript,
        }),
      ]);
    } catch (error) {
      this.error('Error injecting control tab scripts early:', error);
      throw error;
    }
  }

  private async injectBrowserControllerProxyInTarget({
    cdp,
    targetSessionId,
  }: {
    cdp: CDP;
    targetSessionId: string;
  }) {
    const browserControllerProxyUrl = getHashedScriptUrl(
      this.env.KAKU_API_ENDPOINT,
      'browser-controller-proxy.min.js',
    );

    const browserControllerProxyScript = await fetch(browserControllerProxyUrl).then((r) =>
      r.text(),
    );

    // These scripts will persist across page navigations
    await cdp!.Page.addScriptToEvaluateOnNewDocument(
      {
        source: browserControllerProxyScript,
        worldName: 'kaku-target-world',
      },
      targetSessionId,
    );
  }

  /**
   * Enable required CDP domains for both control and target tabs
   */
  private async enableDomainsForBothTabs({
    cdp,
    controlTabSessionId,
    targetSessionId,
  }: {
    cdp: CDP;
    controlTabSessionId: string;
    targetSessionId: string;
  }): Promise<void> {
    // Enable domains for control tab
    await Promise.all([
      cdp.Page.enable(undefined, controlTabSessionId),
      cdp.Runtime.enable(undefined, controlTabSessionId),
    ]);

    // Enable domains for target tab
    await Promise.all([
      cdp.Page.enable(undefined, targetSessionId),
      cdp.Runtime.enable(undefined, targetSessionId),
    ]);

    // Disable content security policy for both tabs
    await Promise.all([
      cdp.Page.setBypassCSP({ enabled: true }, controlTabSessionId),
      cdp.Page.setBypassCSP({ enabled: true }, targetSessionId),
    ]);

    await Promise.all([cdp.WebAuthn.enable(undefined, targetSessionId)]);

    const virtualAuthenticatorOptions = {
      protocol: 'ctap2' as const,
      transport: 'internal' as const,
      hasResidentKey: true,
      hasUserVerification: true,
      isUserVerified: true,
      automaticPresenceSimulation: true,
    };
    await cdp.WebAuthn.addVirtualAuthenticator(
      { options: virtualAuthenticatorOptions },
      targetSessionId,
    );
  }

  private async handleBrowserError(error: any): Promise<void> {
    this.error('An error occurred while setting up the browser session:', error);
    if (error instanceof Error) {
      await ErrorService.handleBrowserConnectionError(
        error,
        {
          ...this.eventPayload,
          referenceId: this.eventPayload.linkId,
        },
        this.env,
      );
    }
  }

  private async injectScriptOnNewDocument({
    cdp,
    script,
    sessionId,
  }: {
    cdp: CDP;
    script: string;
    sessionId: string;
  }) {
    await cdp.Page.addScriptToEvaluateOnNewDocument(
      {
        source: wrapForMainFrameOnly(script),
      },
      sessionId,
    );
  }

  private async injectScriptWithEvaluate({
    cdp,
    sessionId,
    script,
  }: {
    cdp: CDP;
    sessionId: string;
    script: string;
  }) {
    const result = await cdp.Runtime.evaluate(
      {
        expression: script,
        awaitPromise: true,
      },
      sessionId,
    );
    if (result.exceptionDetails) {
      console.error('Script injection error:', result.exceptionDetails);
      throw new Error(`Script injection failed: ${result.exceptionDetails.text}`);
    }
  }
}

const sControlTabScript =
  '(function () {\n' +
  '  "use strict";\n' +
  '\n' +
  '  // Simple CDP implementation for browser environment\n' +
  '  class CDP {\n' +
  '    constructor(targetInfo) {\n' +
  '      this.targetInfo = targetInfo;\n' +
  '      this.ws = null;\n' +
  '      this.messageId = 0;\n' +
  '      this.pendingMessages = new Map();\n' +
  '    }\n' +
  '\n' +
  '    async connect() {\n' +
  '      if (this.ws) return;\n' +
  '\n' +
  '      this.ws = new WebSocket(this.targetInfo.webSocketDebuggerUrl);\n' +
  '\n' +
  '      return new Promise((resolve, reject) => {\n' +
  '        this.ws.onopen = () => {\n' +
  '          console.log(`Connected to target: ${this.targetInfo.title}`);\n' +
  '          resolve();\n' +
  '        };\n' +
  '\n' +
  '        this.ws.onerror = reject;\n' +
  '\n' +
  '        this.ws.onmessage = (event) => {\n' +
  '          const message = JSON.parse(event.data);\n' +
  '\n' +
  '          if (message.id && this.pendingMessages.has(message.id)) {\n' +
  '            const { resolve, reject } = this.pendingMessages.get(message.id);\n' +
  '            this.pendingMessages.delete(message.id);\n' +
  '\n' +
  '            if (message.error) {\n' +
  '              reject(new Error(message.error.message));\n' +
  '            } else {\n' +
  '              resolve(message.result);\n' +
  '            }\n' +
  '          }\n' +
  '        };\n' +
  '      });\n' +
  '    }\n' +
  '\n' +
  '    async send(method, params = {}, sessionId = null) {\n' +
  '      if (!this.ws) {\n' +
  '        await this.connect();\n' +
  '      }\n' +
  '\n' +
  '      const id = ++this.messageId;\n' +
  '      const message = { id, method, params };\n' +
  '\n' +
  '      // Add sessionId if provided (for remote debugging)\n' +
  '      if (sessionId) {\n' +
  '        message.sessionId = sessionId;\n' +
  '      }\n' +
  '\n' +
  '      return new Promise((resolve, reject) => {\n' +
  '        this.pendingMessages.set(id, { resolve, reject });\n' +
  '        this.ws.send(JSON.stringify(message));\n' +
  '      });\n' +
  '    }\n' +
  '\n' +
  '    // Runtime domain\n' +
  '    get Runtime() {\n' +
  '      return {\n' +
  '        enable: (params = {}, sessionId = null) =>\n' +
  '          this.send("Runtime.enable", params, sessionId),\n' +
  '        evaluate: (params, sessionId = null) =>\n' +
  '          this.send("Runtime.evaluate", params, sessionId),\n' +
  '      };\n' +
  '    }\n' +
  '\n' +
  '    // Target domain\n' +
  '    get Target() {\n' +
  '      return {\n' +
  '        getTargets: (params = {}, sessionId = null) =>\n' +
  '          this.send("Target.getTargets", params, sessionId),\n' +
  '        createTarget: (params, sessionId = null) =>\n' +
  '          this.send("Target.createTarget", params, sessionId),\n' +
  '        attachToTarget: (params, sessionId = null) =>\n' +
  '          this.send("Target.attachToTarget", params, sessionId),\n' +
  '        closeTarget: (params, sessionId = null) =>\n' +
  '          this.send("Target.closeTarget", params, sessionId),\n' +
  '        activateTarget: (params, sessionId = null) =>\n' +
  '          this.send("Target.activateTarget", params, sessionId),\n' +
  '      };\n' +
  '    }\n' +
  '\n' +
  '    // Input domain\n' +
  '    get Input() {\n' +
  '      return {\n' +
  '        dispatchKeyEvent: (params, sessionId = null) =>\n' +
  '          this.send("Input.dispatchKeyEvent", params, sessionId),\n' +
  '        dispatchMouseEvent: (params, sessionId = null) =>\n' +
  '          this.send("Input.dispatchMouseEvent", params, sessionId),\n' +
  '      };\n' +
  '    }\n' +
  '\n' +
  '    async close() {\n' +
  '      if (this.ws) {\n' +
  '        this.ws.close();\n' +
  '        this.ws = null;\n' +
  '      }\n' +
  '    }\n' +
  '  }\n' +
  '\n' +
  '  console.log("[POC-Streaming] Control tab script initializing...");\n' +
  '\n' +
  '  // Prevent multiple injections\n' +
  '  if (window.controlTabInjected) {\n' +
  '    console.log(\n' +
  '      "[POC-Streaming] Control tab script already injected, skipping..."\n' +
  '    );\n' +
  '    return;\n' +
  '  }\n' +
  '  window.controlTabInjected = true;\n' +
  '\n' +
  '  class ControlTabManager {\n' +
  '    constructor() {\n' +
  '      this.signalingServerUrl = "ws://localhost:8080"; // Will be set dynamically\n' +
  '      this.websocket = null;\n' +
  '      this.isConnected = false;\n' +
  '\n' +
  '      // WebRTC configuration\n' +
  '      this.rtcConfig = {\n' +
  '        iceServers: [\n' +
  '          { urls: "stun:stun.cloudflare.com:3478" },\n' +
  '          { urls: "stun:stun.l.google.com:19302" },\n' +
  '        ],\n' +
  '        iceCandidatePoolSize: 10,\n' +
  '      };\n' +
  '\n' +
  '      // Connection management - refactored for multi-client support\n' +
  '      this.peerConnections = new Map(); // webClientId -> RTCPeerConnection (to web client)\n' +
  '      this.targetConnections = new Map(); // tabId -> RTCPeerConnection (to target tab)\n' +
  '      this.targetTabs = new Map(); // tabId -> tabInfo\n' +
  '      this.activeStreams = new Map(); // tabId -> { webClientId, peerConnection }\n' +
  '      this.dataChannels = new Map(); // webClientId -> dataChannel\n' +
  '      this.webClients = new Map(); // webClientId -> { clientInfo, currentTabId }\n' +
  '\n' +
  '      // CDP connection management using simple-cdp\n' +
  '      this.cdpClients = new Map(); // targetTabId -> CDP instance\n' +
  '      this.cdpSessions = new Map(); // targetTabId -> sessionId\n' +
  '\n' +
  '      this.init();\n' +
  '    }\n' +
  '\n' +
  '    async init() {\n' +
  '      console.log("[POC-Streaming] Initializing control tab manager...");\n' +
  '      await this.connectToSignalingServer();\n' +
  '      this.setupPageListeners();\n' +
  '      this.createControlTabUI();\n' +
  '    }\n' +
  '\n' +
  '    async connectToSignalingServer() {\n' +
  '      try {\n' +
  '        console.log("[POC-Streaming] Connecting to signaling server...");\n' +
  '        this.websocket = new WebSocket(this.signalingServerUrl);\n' +
  '\n' +
  '        this.websocket.onopen = () => {\n' +
  '          console.log(\n' +
  '            "[POC-Streaming] Control tab connected to signaling server"\n' +
  '          );\n' +
  '          this.isConnected = true;\n' +
  '\n' +
  '          // Register as control tab\n' +
  '          this.sendMessage({\n' +
  '            type: "register-control-tab",\n' +
  '            metadata: {\n' +
  '              userAgent: navigator.userAgent,\n' +
  '              timestamp: Date.now(),\n' +
  '            },\n' +
  '          });\n' +
  '        };\n' +
  '\n' +
  '        this.websocket.onmessage = (event) => {\n' +
  '          try {\n' +
  '            const message = JSON.parse(event.data);\n' +
  '            this.handleMessage(message);\n' +
  '          } catch (error) {\n' +
  '            console.error("[POC-Streaming] Failed to parse message:", error);\n' +
  '          }\n' +
  '        };\n' +
  '\n' +
  '        this.websocket.onclose = () => {\n' +
  '          console.log(\n' +
  '            "[POC-Streaming] Control tab disconnected from signaling server"\n' +
  '          );\n' +
  '          this.isConnected = false;\n' +
  '          this.scheduleReconnect();\n' +
  '        };\n' +
  '\n' +
  '        this.websocket.onerror = (error) => {\n' +
  '          console.error("[POC-Streaming] Control tab WebSocket error:", error);\n' +
  '        };\n' +
  '      } catch (error) {\n' +
  '        console.error(\n' +
  '          "[POC-Streaming] Failed to connect to signaling server:",\n' +
  '          error\n' +
  '        );\n' +
  '        this.scheduleReconnect();\n' +
  '      }\n' +
  '    }\n' +
  '\n' +
  '    scheduleReconnect() {\n' +
  '      console.log("[POC-Streaming] Scheduling reconnection in 5 seconds...");\n' +
  '      setTimeout(() => {\n' +
  '        this.connectToSignalingServer();\n' +
  '      }, 5000);\n' +
  '    }\n' +
  '\n' +
  '    sendMessage(message) {\n' +
  '      if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {\n' +
  '        this.websocket.send(JSON.stringify(message));\n' +
  '      } else {\n' +
  '        console.warn("[POC-Streaming] Cannot send message - not connected");\n' +
  '      }\n' +
  '    }\n' +
  '\n' +
  '    handleMessage(message) {\n' +
  '      console.log(\n' +
  '        "[POC-Streaming] Control tab received message:",\n' +
  '        message.type\n' +
  '      );\n' +
  '\n' +
  '      switch (message.type) {\n' +
  '        case "target-tabs-list":\n' +
  '          this.handleTargetTabsList(message);\n' +
  '          break;\n' +
  '        case "target-tab-registered":\n' +
  '          this.handleTargetTabRegistered(message);\n' +
  '          break;\n' +
  '        case "target-tab-disconnected":\n' +
  '          // this.handleTargetTabDisconnected(message);\n' +
  '          break;\n' +
  '        case "stream-stopped":\n' +
  '          // this.handleStreamStopped(message);\n' +
  '          break;\n' +
  '        case "webrtc-offer":\n' +
  '          this.handleWebRTCOffer(message);\n' +
  '          break;\n' +
  '        case "webrtc-answer":\n' +
  '          this.handleWebRTCAnswer(message);\n' +
  '          break;\n' +
  '        case "webrtc-ice-candidate":\n' +
  '          this.handleWebRTCIceCandidate(message);\n' +
  '          break;\n' +
  '        case "webrtc-offer-from-target":\n' +
  '          this.handleTargetTabOffer(message);\n' +
  '          break;\n' +
  '        case "webrtc-ice-candidate-from-target":\n' +
  '          this.handleTargetTabIceCandidate(message);\n' +
  '          break;\n' +
  '        case "webrtc-answer-from-web-client":\n' +
  '          this.handleWebClientAnswer(message);\n' +
  '          break;\n' +
  '        case "webrtc-ice-candidate-from-web-client":\n' +
  '          this.handleWebClientIceCandidate(message);\n' +
  '          break;\n' +
  '        case "web-client-registered":\n' +
  '          this.handleWebClientRegistered(message);\n' +
  '          break;\n' +
  '        case "web-client-disconnected":\n' +
  '          this.handleWebClientDisconnected(message);\n' +
  '          break;\n' +
  '        default:\n' +
  '          console.log("[POC-Streaming] Unknown message type:", message.type);\n' +
  '      }\n' +
  '    }\n' +
  '\n' +
  '    handleTargetTabsList(message) {\n' +
  '      console.log(\n' +
  '        "[POC-Streaming] Received target tabs list:",\n' +
  '        message.targetTabs.length\n' +
  '      );\n' +
  '\n' +
  '      // Update target tabs\n' +
  '      this.targetTabs.clear();\n' +
  '      message.targetTabs.forEach((tab) => {\n' +
  '        this.targetTabs.set(tab.tabId, tab);\n' +
  '      });\n' +
  '    }\n' +
  '\n' +
  '    handleTargetTabRegistered(message) {\n' +
  '      console.log("[POC-Streaming] Target tab registered:", message.tabId);\n' +
  '      this.targetTabs.set(message.tabId, message);\n' +
  '    }\n' +
  '\n' +
  '    handleTargetTabDisconnected(message) {\n' +
  '      console.log("[POC-Streaming] Target tab disconnected:", message.tabId);\n' +
  '      this.targetTabs.delete(message.tabId);\n' +
  '\n' +
  '      // Clean up any active streams for this tab\n' +
  '      if (this.activeStreams.has(message.tabId)) {\n' +
  '        this.cleanupStream(message.tabId);\n' +
  '      }\n' +
  '    }\n' +
  '\n' +
  '    handleWebClientRegistered(message) {\n' +
  '      console.log(\n' +
  '        "[POC-Streaming] Web client registered:",\n' +
  '        message.webClientId\n' +
  '      );\n' +
  '\n' +
  '      // Store web client info\n' +
  '      this.webClients.set(message.webClientId, {\n' +
  '        clientInfo: message.metadata || {},\n' +
  '        currentTabId: null,\n' +
  '        connected: true,\n' +
  '      });\n' +
  '\n' +
  '      // Create dedicated peer connection for this web client\n' +
  '      this.createPeerConnectionForWebClient(message.webClientId);\n' +
  '    }\n' +
  '\n' +
  '    handleWebClientDisconnected(message) {\n' +
  '      console.log(\n' +
  '        "[POC-Streaming] Web client disconnected:",\n' +
  '        message.webClientId\n' +
  '      );\n' +
  '\n' +
  '      // Clean up peer connection and resources for this client\n' +
  '      this.cleanupWebClient(message.webClientId);\n' +
  '    }\n' +
  '\n' +
  '    handleStreamStopped(message) {\n' +
  '      console.log(\n' +
  '        "[POC-Streaming] Stream stopped for tab:",\n' +
  '        message.targetTabId\n' +
  '      );\n' +
  '      this.cleanupStream(message.targetTabId);\n' +
  '    }\n' +
  '\n' +
  '    async handleWebRTCOffer() {\n' +
  '      console.log("[POC-Streaming] Received WebRTC offer");\n' +
  '      // This would be handled if control tab receives offers (not typical in this architecture)\n' +
  '    }\n' +
  '\n' +
  '    async handleWebRTCAnswer(message) {\n' +
  '      console.log(\n' +
  '        "[POC-Streaming] Received WebRTC answer for web client:",\n' +
  '        message.webClientId\n' +
  '      );\n' +
  '\n' +
  '      const peerConnection = this.peerConnections.get(message.webClientId);\n' +
  '      if (peerConnection) {\n' +
  '        try {\n' +
  '          await peerConnection.setRemoteDescription(\n' +
  '            new RTCSessionDescription(message.answer)\n' +
  '          );\n' +
  '          console.log(\n' +
  '            "[POC-Streaming] WebRTC connection established for web client:",\n' +
  '            message.webClientId\n' +
  '          );\n' +
  '        } catch (error) {\n' +
  '          console.error(\n' +
  '            "[POC-Streaming] Failed to set remote description:",\n' +
  '            error\n' +
  '          );\n' +
  '        }\n' +
  '      }\n' +
  '    }\n' +
  '\n' +
  '    async handleWebRTCIceCandidate(message) {\n' +
  '      const peerConnection = this.peerConnections.get(message.webClientId);\n' +
  '      if (peerConnection) {\n' +
  '        try {\n' +
  '          await peerConnection.addIceCandidate(message.candidate);\n' +
  '        } catch (error) {\n' +
  '          console.error("[POC-Streaming] Failed to add ICE candidate:", error);\n' +
  '        }\n' +
  '      }\n' +
  '    }\n' +
  '\n' +
  '    async handleTargetTabOffer(message) {\n' +
  '      console.log(\n' +
  '        "[POC-Streaming] Received WebRTC offer from target tab:",\n' +
  '        message\n' +
  '      );\n' +
  '\n' +
  '      // Create stream info from the message since target tab is initiating\n' +
  '      const streamInfo = {\n' +
  '        targetTabId: message.targetTabId,\n' +
  '        status: "connecting",\n' +
  '      };\n' +
  '\n' +
  '      // Create peer connection to target tab\n' +
  '      const targetPeerConnection = new RTCPeerConnection(this.rtcConfig);\n' +
  '\n' +
  '      // Store target connection using tabId as key\n' +
  '      this.targetConnections.set(message.targetTabId, targetPeerConnection);\n' +
  '\n' +
  '      // Handle incoming stream from target tab\n' +
  '      targetPeerConnection.ontrack = (event) => {\n' +
  '        console.log("[POC-Streaming] Target stream event:", event);\n' +
  '        const [stream] = event.streams;\n' +
  '\n' +
  '        // Display the stream in control tab\n' +
  '        this.displayStreamInControlTab(message.targetTabId, stream, streamInfo);\n' +
  '\n' +
  '        // Broadcast stream to ALL connected web clients\n' +
  '        this.broadcastStreamToAllClients(stream, message.targetTabId);\n' +
  '      };\n' +
  '\n' +
  '      // Handle ICE candidates from target tab\n' +
  '      targetPeerConnection.onicecandidate = (event) => {\n' +
  '        if (event.candidate) {\n' +
  '          // Send ICE candidate back to target tab\n' +
  '          this.sendMessage({\n' +
  '            type: "webrtc-ice-candidate-to-target",\n' +
  '            candidate: event.candidate,\n' +
  '            targetTabId: streamInfo.targetTabId,\n' +
  '          });\n' +
  '        }\n' +
  '      };\n' +
  '\n' +
  '      // Accept the offer from target tab\n' +
  '      try {\n' +
  '        await targetPeerConnection.setRemoteDescription(\n' +
  '          new RTCSessionDescription(message.offer)\n' +
  '        );\n' +
  '        const answer = await targetPeerConnection.createAnswer();\n' +
  '        await targetPeerConnection.setLocalDescription(answer);\n' +
  '\n' +
  '        // Send answer back to target tab\n' +
  '        this.sendMessage({\n' +
  '          type: "webrtc-answer-to-target",\n' +
  '          answer: answer,\n' +
  '          targetTabId: streamInfo.targetTabId,\n' +
  '        });\n' +
  '\n' +
  '        console.log("[POC-Streaming] WebRTC answer sent to target tab");\n' +
  '      } catch (error) {\n' +
  '        console.error(\n' +
  '          "[POC-Streaming] Failed to handle target tab offer:",\n' +
  '          error\n' +
  '        );\n' +
  '      }\n' +
  '    }\n' +
  '\n' +
  '    async handleTargetTabIceCandidate(message) {\n' +
  '      const targetPeerConnection = this.targetConnections.get(\n' +
  '        message.targetTabId\n' +
  '      );\n' +
  '      if (targetPeerConnection) {\n' +
  '        try {\n' +
  '          await targetPeerConnection.addIceCandidate(message.candidate);\n' +
  '        } catch (error) {\n' +
  '          console.error(\n' +
  '            "[POC-Streaming] Failed to add target ICE candidate:",\n' +
  '            error\n' +
  '          );\n' +
  '        }\n' +
  '      }\n' +
  '    }\n' +
  '\n' +
  '    async handleWebClientAnswer(message) {\n' +
  '      console.log(\n' +
  '        "[POC-Streaming] Received WebRTC answer from web client:",\n' +
  '        message.webClientId\n' +
  '      );\n' +
  '\n' +
  '      const peerConnection = this.peerConnections.get(message.webClientId);\n' +
  '      if (peerConnection) {\n' +
  '        try {\n' +
  '          await peerConnection.setRemoteDescription(\n' +
  '            new RTCSessionDescription(message.answer)\n' +
  '          );\n' +
  '          console.log(\n' +
  '            "[POC-Streaming] WebRTC connection established with web client:",\n' +
  '            message.webClientId\n' +
  '          );\n' +
  '        } catch (error) {\n' +
  '          console.error(\n' +
  '            "[POC-Streaming] Failed to set remote description from web client:",\n' +
  '            error\n' +
  '          );\n' +
  '        }\n' +
  '      } else {\n' +
  '        console.warn(\n' +
  '          "[POC-Streaming] No peer connection found for web client:",\n' +
  '          message.webClientId\n' +
  '        );\n' +
  '      }\n' +
  '    }\n' +
  '\n' +
  '    async handleWebClientIceCandidate(message) {\n' +
  '      console.log(\n' +
  '        "[POC-Streaming] Received ICE candidate from web client:",\n' +
  '        message.webClientId\n' +
  '      );\n' +
  '\n' +
  '      const peerConnection = this.peerConnections.get(message.webClientId);\n' +
  '      if (peerConnection) {\n' +
  '        try {\n' +
  '          await peerConnection.addIceCandidate(message.candidate);\n' +
  '        } catch (error) {\n' +
  '          console.error(\n' +
  '            "[POC-Streaming] Failed to add ICE candidate from web client:",\n' +
  '            error\n' +
  '          );\n' +
  '        }\n' +
  '      } else {\n' +
  '        console.warn(\n' +
  '          "[POC-Streaming] No peer connection found for web client:",\n' +
  '          message.webClientId\n' +
  '        );\n' +
  '      }\n' +
  '    }\n' +
  '\n' +
  '    createPeerConnectionForWebClient(webClientId) {\n' +
  '      console.log(\n' +
  '        "[POC-Streaming] Creating peer connection for web client:",\n' +
  '        webClientId\n' +
  '      );\n' +
  '\n' +
  '      const peerConnection = new RTCPeerConnection(this.rtcConfig);\n' +
  '\n' +
  '      // Create data channel for user events\n' +
  '      const dataChannel = peerConnection.createDataChannel("userEvents", {\n' +
  '        ordered: true,\n' +
  '      });\n' +
  '      this.setupDataChannelHandlers(dataChannel, webClientId);\n' +
  '      this.dataChannels.set(webClientId, dataChannel);\n' +
  '\n' +
  '      // Handle ICE candidates from web client\n' +
  '      peerConnection.onicecandidate = (event) => {\n' +
  '        if (event.candidate) {\n' +
  '          this.sendMessage({\n' +
  '            type: "webrtc-ice-candidate-to-web-client",\n' +
  '            candidate: event.candidate,\n' +
  '            webClientId: webClientId,\n' +
  '          });\n' +
  '        }\n' +
  '      };\n' +
  '\n' +
  '      // Store the peer connection\n' +
  '      this.peerConnections.set(webClientId, peerConnection);\n' +
  '\n' +
  '      console.log(\n' +
  '        "[POC-Streaming] Peer connection created for web client:",\n' +
  '        webClientId\n' +
  '      );\n' +
  '    }\n' +
  '\n' +
  '    cleanupWebClient(webClientId) {\n' +
  '      console.log("[POC-Streaming] Cleaning up web client:", webClientId);\n' +
  '\n' +
  '      // Close and remove peer connection\n' +
  '      const peerConnection = this.peerConnections.get(webClientId);\n' +
  '      if (peerConnection) {\n' +
  '        peerConnection.close();\n' +
  '        this.peerConnections.delete(webClientId);\n' +
  '      }\n' +
  '\n' +
  '      // Clean up data channel\n' +
  '      const dataChannel = this.dataChannels.get(webClientId);\n' +
  '      if (dataChannel) {\n' +
  '        dataChannel.close();\n' +
  '        this.dataChannels.delete(webClientId);\n' +
  '      }\n' +
  '\n' +
  '      // Remove from web clients map\n' +
  '      this.webClients.delete(webClientId);\n' +
  '\n' +
  '      console.log("[POC-Streaming] Web client cleanup completed:", webClientId);\n' +
  '    }\n' +
  '\n' +
  '    broadcastStreamToAllClients(stream, targetTabId) {\n' +
  '      console.log(\n' +
  '        "[POC-Streaming] Broadcasting stream to all connected web clients for tab:",\n' +
  '        targetTabId\n' +
  '      );\n' +
  '\n' +
  '      // Iterate through all connected web clients\n' +
  '      for (const [webClientId, peerConnection] of this.peerConnections) {\n' +
  '        console.log(\n' +
  '          "[POC-Streaming] Processing web client:",\n' +
  '          webClientId,\n' +
  '          "- Connection state:",\n' +
  '          peerConnection.connectionState\n' +
  '        );\n' +
  '\n' +
  "        // Update client's current tab\n" +
  '        const clientInfo = this.webClients.get(webClientId);\n' +
  '        if (clientInfo) {\n' +
  '          clientInfo.currentTabId = targetTabId;\n' +
  '        }\n' +
  '\n' +
  '        // Add or replace tracks for this client\n' +
  '        stream.getTracks().forEach((track) => {\n' +
  '          const sender = peerConnection\n' +
  '            .getSenders()\n' +
  '            .find((s) => s.track && s.track.kind === track.kind);\n' +
  '\n' +
  '          if (sender) {\n' +
  '            console.log(\n' +
  '              `[POC-Streaming] Replacing ${track.kind} track for client:`,\n' +
  '              webClientId\n' +
  '            );\n' +
  '            sender.replaceTrack(track);\n' +
  '          } else {\n' +
  '            console.log(\n' +
  '              `[POC-Streaming] Adding ${track.kind} track for client:`,\n' +
  '              webClientId\n' +
  '            );\n' +
  '            peerConnection.addTrack(track, stream);\n' +
  '          }\n' +
  '        });\n' +
  '\n' +
  '        // Check connection state before creating new offer\n' +
  '        const connectionState = peerConnection.connectionState;\n' +
  '        const signalingState = peerConnection.signalingState;\n' +
  '\n' +
  '        if (\n' +
  '          connectionState === "connected" ||\n' +
  '          connectionState === "completed"\n' +
  '        ) {\n' +
  '          console.log(\n' +
  '            `[POC-Streaming] Client ${webClientId} already connected - tracks updated via existing connection`\n' +
  '          );\n' +
  '        } else if (\n' +
  '          signalingState === "stable" &&\n' +
  '          (connectionState === "new" || connectionState === "connecting")\n' +
  '        ) {\n' +
  '          console.log(\n' +
  '            `[POC-Streaming] Creating new offer for client ${webClientId} (connection: ${connectionState}, signaling: ${signalingState})`\n' +
  '          );\n' +
  '          this.createOfferToWebClient(targetTabId, webClientId);\n' +
  '        } else if (signalingState !== "stable") {\n' +
  '          console.log(\n' +
  '            `[POC-Streaming] Skipping offer for client ${webClientId} - negotiation in progress (signaling: ${signalingState})`\n' +
  '          );\n' +
  '        } else {\n' +
  '          console.log(\n' +
  '            `[POC-Streaming] Creating offer for client ${webClientId} (connection: ${connectionState}, signaling: ${signalingState})`\n' +
  '          );\n' +
  '          this.createOfferToWebClient(targetTabId, webClientId);\n' +
  '        }\n' +
  '      }\n' +
  '\n' +
  '      console.log(\n' +
  '        "[POC-Streaming] Stream broadcast completed to",\n' +
  '        this.peerConnections.size,\n' +
  '        "clients"\n' +
  '      );\n' +
  '    }\n' +
  '\n' +
  '    async createOfferToWebClient(tabId, webClientId) {\n' +
  '      const peerConnection = this.peerConnections.get(webClientId);\n' +
  '      if (!peerConnection) {\n' +
  '        console.error(\n' +
  '          "[POC-Streaming] No peer connection found for web client:",\n' +
  '          webClientId\n' +
  '        );\n' +
  '        return;\n' +
  '      }\n' +
  '\n' +
  '      // Double-check connection state before creating offer\n' +
  '      const connectionState = peerConnection.connectionState;\n' +
  '      const signalingState = peerConnection.signalingState;\n' +
  '\n' +
  '      console.log(\n' +
  '        `[POC-Streaming] Creating offer for client ${webClientId} - Connection: ${connectionState}, Signaling: ${signalingState}`\n' +
  '      );\n' +
  '\n' +
  '      try {\n' +
  '        const offer = await peerConnection.createOffer();\n' +
  '        await peerConnection.setLocalDescription(offer);\n' +
  '\n' +
  '        // Send offer to web client\n' +
  '        this.sendMessage({\n' +
  '          type: "webrtc-offer-to-web-client",\n' +
  '          offer: offer,\n' +
  '          targetClientId: webClientId,\n' +
  '          tabId: tabId,\n' +
  '          fromClientId: this.clientId,\n' +
  '        });\n' +
  '\n' +
  '        console.log(\n' +
  '          `[POC-Streaming] WebRTC offer sent to web client: ${webClientId} (${connectionState} -> negotiating)`\n' +
  '        );\n' +
  '      } catch (error) {\n' +
  '        console.error(\n' +
  '          `[POC-Streaming] Failed to create offer to web client ${webClientId}:`,\n' +
  '          error.message\n' +
  '        );\n' +
  '\n' +
  '        // Log additional context for debugging\n' +
  '        console.error(\n' +
  '          `[POC-Streaming] Error context - Connection: ${connectionState}, Signaling: ${signalingState}`\n' +
  '        );\n' +
  '      }\n' +
  '    }\n' +
  '\n' +
  '    cleanupStream(tabId) {\n' +
  '      console.log("[POC-Streaming] Cleaning up stream for tab:", tabId);\n' +
  '\n' +
  '      // Clean up target connection\n' +
  '      const targetConnection = this.targetConnections.get(tabId);\n' +
  '      if (targetConnection) {\n' +
  '        targetConnection.close();\n' +
  '        this.targetConnections.delete(tabId);\n' +
  '      }\n' +
  '\n' +
  '      // Update all web clients to remove tracks from this tab\n' +
  '      for (const [webClientId, clientInfo] of this.webClients) {\n' +
  '        if (clientInfo.currentTabId === tabId) {\n' +
  '          clientInfo.currentTabId = null;\n' +
  '\n' +
  "          // Remove tracks from this client's peer connection\n" +
  '          const peerConnection = this.peerConnections.get(webClientId);\n' +
  '          if (peerConnection) {\n' +
  '            peerConnection.getSenders().forEach((sender) => {\n' +
  '              if (sender.track) {\n' +
  '                peerConnection.removeTrack(sender);\n' +
  '              }\n' +
  '            });\n' +
  '          }\n' +
  '        }\n' +
  '      }\n' +
  '\n' +
  '      // Remove from UI\n' +
  '      const streamElement = document.getElementById(`poc-stream-${tabId}`);\n' +
  '      if (streamElement) {\n' +
  '        streamElement.remove();\n' +
  '      }\n' +
  '\n' +
  '      // Add "no streams" message if no streams left\n' +
  '      const streamsContainer = document.getElementById("poc-streams-container");\n' +
  '      if (streamsContainer && streamsContainer.children.length === 0) {\n' +
  '        streamsContainer.innerHTML =\n' +
  '          \'<div style="color: #666; font-style: italic;">No active streams</div>\';\n' +
  '      }\n' +
  '\n' +
  '      this.activeStreams.delete(tabId);\n' +
  '    }\n' +
  '\n' +
  '    createControlTabUI() {\n' +
  '      // Create a floating control panel for the control tab\n' +
  '      const controlPanel = document.createElement("div");\n' +
  '      controlPanel.id = "poc-control-panel";\n' +
  '      controlPanel.style.cssText = `\n' +
  '        position: fixed;\n' +
  '        top: 20px;\n' +
  '        right: 20px;\n' +
  '        width: 400px;\n' +
  '        max-height: 600px;\n' +
  '        background: rgba(0, 0, 0, 0.9);\n' +
  '        color: white;\n' +
  '        border-radius: 8px;\n' +
  '        padding: 16px;\n' +
  '        z-index: 10000;\n' +
  "        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\n" +
  '        font-size: 14px;\n' +
  '        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);\n' +
  '        overflow-y: auto;\n' +
  '      `;\n' +
  '\n' +
  '      controlPanel.innerHTML = `\n' +
  '        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">\n' +
  '          <h3 style="margin: 0; color: #4CAF50;">POC Control Tab</h3>\n' +
  '          <button id="poc-toggle-panel" style="background: none; border: 1px solid #666; color: white; padding: 4px 8px; border-radius: 4px; cursor: pointer;">−</button>\n' +
  '        </div>\n' +
  '        <div id="poc-panel-content">\n' +
  '          <div style="margin-bottom: 12px;">\n' +
  '            <div style="font-weight: bold; margin-bottom: 4px;">Status:</div>\n' +
  '            <div id="poc-connection-status" style="color: #ff9800;">Connecting...</div>\n' +
  '          </div>\n' +
  '          <div style="margin-bottom: 12px;">\n' +
  '            <div style="font-weight: bold; margin-bottom: 4px;">Active Streams:</div>\n' +
  '            <div id="poc-streams-container" style="max-height: 300px; overflow-y: auto;">\n' +
  '              <div style="color: #666; font-style: italic;">No active streams</div>\n' +
  '            </div>\n' +
  '          </div>\n' +
  '          <div style="margin-bottom: 12px;">\n' +
  '            <div style="font-weight: bold; margin-bottom: 4px;">Target Tabs:</div>\n' +
  '            <div id="poc-tabs-container" style="max-height: 200px; overflow-y: auto;">\n' +
  '              <div style="color: #666; font-style: italic;">No target tabs</div>\n' +
  '            </div>\n' +
  '          </div>\n' +
  '        </div>\n' +
  '      `;\n' +
  '\n' +
  '      document.body.appendChild(controlPanel);\n' +
  '\n' +
  '      // Add toggle functionality\n' +
  '      const toggleBtn = document.getElementById("poc-toggle-panel");\n' +
  '      const panelContent = document.getElementById("poc-panel-content");\n' +
  '      let isCollapsed = false;\n' +
  '\n' +
  '      toggleBtn.addEventListener("click", () => {\n' +
  '        isCollapsed = !isCollapsed;\n' +
  '        panelContent.style.display = isCollapsed ? "none" : "block";\n' +
  '        toggleBtn.textContent = isCollapsed ? "+" : "−";\n' +
  '        controlPanel.style.height = isCollapsed ? "auto" : "";\n' +
  '      });\n' +
  '\n' +
  '      // Update connection status\n' +
  '      this.updateConnectionStatus("Connected");\n' +
  '    }\n' +
  '\n' +
  '    updateConnectionStatus(status) {\n' +
  '      const statusElement = document.getElementById("poc-connection-status");\n' +
  '      if (statusElement) {\n' +
  '        statusElement.textContent = status;\n' +
  '        statusElement.style.color =\n' +
  '          status === "Connected" ? "#4CAF50" : "#ff9800";\n' +
  '      }\n' +
  '    }\n' +
  '\n' +
  '    displayStreamInControlTab(tabId, mediaStream, streamInfo) {\n' +
  '      console.log(\n' +
  '        "[POC-Streaming] Displaying stream in control tab for tab:",\n' +
  '        tabId\n' +
  '      );\n' +
  '\n' +
  '      const streamsContainer = document.getElementById("poc-streams-container");\n' +
  '      if (!streamsContainer) {\n' +
  '        console.warn("[POC-Streaming] Streams container not found");\n' +
  '        return;\n' +
  '      }\n' +
  '\n' +
  '      // Remove "no streams" message if present\n' +
  '      const noStreamsMsg = streamsContainer.querySelector(\n' +
  '        \'[style*="font-style: italic"]\'\n' +
  '      );\n' +
  '      if (\n' +
  '        noStreamsMsg &&\n' +
  '        noStreamsMsg.textContent.includes("No active streams")\n' +
  '      ) {\n' +
  '        noStreamsMsg.remove();\n' +
  '      }\n' +
  '\n' +
  '      // Create stream display element\n' +
  '      const streamElement = document.createElement("div");\n' +
  '      streamElement.id = `poc-stream-${tabId}`;\n' +
  '      streamElement.style.cssText = `\n' +
  '        margin-bottom: 12px;\n' +
  '        padding: 8px;\n' +
  '        border: 1px solid #333;\n' +
  '        border-radius: 4px;\n' +
  '        background: rgba(255, 255, 255, 0.05);\n' +
  '      `;\n' +
  '\n' +
  '      streamElement.innerHTML = `\n' +
  '        <div style="font-weight: bold; margin-bottom: 8px; color: #4CAF50;">\n' +
  '          📺 Tab ${tabId.substring(0, 8)}...\n' +
  '        </div>\n' +
  '        <video\n' +
  '          id="poc-video-${tabId}"\n' +
  '          autoplay\n' +
  '          muted\n' +
  '          playsinline\n' +
  '          style="width: 100%; height: 150px; background: #000; border-radius: 4px; object-fit: contain;"\n' +
  '        ></video>\n' +
  '        <div style="margin-top: 8px; font-size: 12px; color: #ccc;">\n' +
  '          Target: ${streamInfo.targetTabId || "Unknown"}\n' +
  '        </div>\n' +
  '      `;\n' +
  '\n' +
  '      streamsContainer.appendChild(streamElement);\n' +
  '\n' +
  '      // Set the video source\n' +
  '      const video = document.getElementById(`poc-video-${tabId}`);\n' +
  '      if (video && mediaStream) {\n' +
  '        video.srcObject = mediaStream;\n' +
  '\n' +
  '        video.onloadedmetadata = () => {\n' +
  '          console.log("[POC-Streaming] Video metadata loaded in control tab");\n' +
  '        };\n' +
  '\n' +
  '        video.onplay = () => {\n' +
  '          console.log("[POC-Streaming] Video started playing in control tab");\n' +
  '        };\n' +
  '\n' +
  '        video.onerror = (error) => {\n' +
  '          console.error("[POC-Streaming] Video error in control tab:", error);\n' +
  '        };\n' +
  '      }\n' +
  '    }\n' +
  '\n' +
  '    setupPageListeners() {\n' +
  '      // Listen for page unload\n' +
  '      window.addEventListener("beforeunload", () => {\n' +
  '        console.log("[POC-Streaming] Control tab unloading...");\n' +
  '\n' +
  '        // Clean up all streams\n' +
  '        for (const tabId of this.activeStreams.keys()) {\n' +
  '          this.cleanupStream(tabId);\n' +
  '        }\n' +
  '\n' +
  '        // Close WebSocket\n' +
  '        if (this.websocket) {\n' +
  '          this.websocket.close();\n' +
  '        }\n' +
  '      });\n' +
  '    }\n' +
  '\n' +
  '    /**\n' +
  '     * Setup data channel handlers for user event replay\n' +
  '     */\n' +
  '    setupDataChannelHandlers(dataChannel, webClientId) {\n' +
  '      console.log(\n' +
  '        "[POC-Streaming] Setting up data channel for web client:",\n' +
  '        webClientId\n' +
  '      );\n' +
  '\n' +
  '      dataChannel.onopen = () => {\n' +
  '        console.log(\n' +
  '          "[POC-Streaming] Data channel opened for web client:",\n' +
  '          webClientId\n' +
  '        );\n' +
  '      };\n' +
  '\n' +
  '      dataChannel.onclose = () => {\n' +
  '        console.log(\n' +
  '          "[POC-Streaming] Data channel closed for web client:",\n' +
  '          webClientId\n' +
  '        );\n' +
  '      };\n' +
  '\n' +
  '      dataChannel.onerror = (error) => {\n' +
  '        console.error("[POC-Streaming] Data channel error:", error);\n' +
  '      };\n' +
  '\n' +
  '      dataChannel.onmessage = (event) => {\n' +
  '        try {\n' +
  '          const userEvent = JSON.parse(event.data);\n' +
  '          console.log("[POC-Streaming] Received user event:", userEvent);\n' +
  '\n' +
  '          // Get the current tab this client is viewing\n' +
  '          const clientInfo = this.webClients.get(webClientId);\n' +
  '          const targetTabId = clientInfo?.currentTabId;\n' +
  '\n' +
  '          if (targetTabId) {\n' +
  '            this.handleUserEvent(userEvent, targetTabId);\n' +
  '          } else {\n' +
  '            console.warn(\n' +
  '              "[POC-Streaming] No target tab for user event from client:",\n' +
  '              webClientId\n' +
  '            );\n' +
  '          }\n' +
  '        } catch (error) {\n' +
  '          console.error("[POC-Streaming] Failed to parse user event:", error);\n' +
  '        }\n' +
  '      };\n' +
  '    }\n' +
  '\n' +
  '    /**\n' +
  '     * Handle user events from web client\n' +
  '     */\n' +
  '    async handleUserEvent(userEvent, targetTabId) {\n' +
  '      if (userEvent.type !== "user-event") {\n' +
  '        console.warn("[POC-Streaming] Unknown event type:", userEvent.type);\n' +
  '        return;\n' +
  '      }\n' +
  '\n' +
  '      console.log(\n' +
  '        "[POC-Streaming] Replaying user event on target tab:",\n' +
  '        targetTabId\n' +
  '      );\n' +
  '\n' +
  '      try {\n' +
  '        await this.replayEventOnTargetTab(userEvent, targetTabId);\n' +
  '      } catch (error) {\n' +
  '        console.error("[POC-Streaming] Failed to replay event:", error);\n' +
  '      }\n' +
  '    }\n' +
  '\n' +
  '    /**\n' +
  '     * Establish persistent CDP session to target tab using simple-cdp\n' +
  '     */\n' +
  '    async establishCDPSession(targetTabId) {\n' +
  '      if (this.cdpClients.has(targetTabId)) {\n' +
  '        return this.cdpClients.get(targetTabId);\n' +
  '      }\n' +
  '\n' +
  '      try {\n' +
  '        console.log(\n' +
  '          "[POC-Streaming] Establishing CDP session to tab:",\n' +
  '          targetTabId\n' +
  '        );\n' +
  '\n' +
  '        // Get target info from CDP\n' +
  '        const tabsResponse = await fetch("http://localhost:9222/json");\n' +
  '        const tabs = await tabsResponse.json();\n' +
  '        const targetTab = tabs.find((tab) => tab.id === targetTabId);\n' +
  '\n' +
  '        if (!targetTab) {\n' +
  '          throw new Error(`Target tab ${targetTabId} not found`);\n' +
  '        }\n' +
  '\n' +
  '        // Create CDP client instance\n' +
  '        const cdpClient = new CDP(targetTab);\n' +
  '        await cdpClient.connect();\n' +
  '\n' +
  '        // Attach to target to create a session\n' +
  '        const attachResult = await cdpClient.Target.attachToTarget({\n' +
  '          targetId: targetTabId,\n' +
  '          flatten: true,\n' +
  '        });\n' +
  '\n' +
  '        const sessionId = attachResult.sessionId;\n' +
  '        console.log("[POC-Streaming] CDP session established:", sessionId);\n' +
  '\n' +
  '        // Store the client and session\n' +
  '        this.cdpClients.set(targetTabId, cdpClient);\n' +
  '        this.cdpSessions.set(targetTabId, sessionId);\n' +
  '\n' +
  '        return cdpClient;\n' +
  '      } catch (error) {\n' +
  '        console.error(\n' +
  '          "[POC-Streaming] Failed to establish CDP session:",\n' +
  '          error\n' +
  '        );\n' +
  '        throw error;\n' +
  '      }\n' +
  '    }\n' +
  '\n' +
  '    /**\n' +
  '     * Execute CDP Input command using simple-cdp\n' +
  '     */\n' +
  '    async executeCDPInputCommand(targetTabId, method, params) {\n' +
  '      try {\n' +
  '        const cdpClient = await this.establishCDPSession(targetTabId);\n' +
  '        const sessionId = this.cdpSessions.get(targetTabId);\n' +
  '\n' +
  '        console.log(\n' +
  '          `[POC-Streaming] Executing ${method} on target tab:`,\n' +
  '          targetTabId\n' +
  '        );\n' +
  '\n' +
  '        // Use simple-cdp to execute the Input command\n' +
  '        if (method === "Input.dispatchMouseEvent") {\n' +
  '          return await cdpClient.Input.dispatchMouseEvent(params, sessionId);\n' +
  '        } else if (method === "Input.dispatchKeyEvent") {\n' +
  '          return await cdpClient.Input.dispatchKeyEvent(params, sessionId);\n' +
  '        } else {\n' +
  '          throw new Error(`Unsupported Input method: ${method}`);\n' +
  '        }\n' +
  '      } catch (error) {\n' +
  '        console.error(\n' +
  '          "[POC-Streaming] Failed to execute CDP Input command:",\n' +
  '          error\n' +
  '        );\n' +
  '        throw error;\n' +
  '      }\n' +
  '    }\n' +
  '\n' +
  '    /**\n' +
  '     * Replay user event on target tab using CDP\n' +
  '     */\n' +
  '    async replayEventOnTargetTab(userEvent, targetTabId) {\n' +
  '      if (userEvent.eventType === "click") {\n' +
  '        await this.replayClickEvent(userEvent, targetTabId);\n' +
  '      } else {\n' +
  '        console.warn(\n' +
  '          "[POC-Streaming] Unsupported event type:",\n' +
  '          userEvent.eventType\n' +
  '        );\n' +
  '      }\n' +
  '    }\n' +
  '\n' +
  '    /**\n' +
  '     * Replay click event on target tab\n' +
  '     */\n' +
  '    async replayClickEvent(userEvent, targetTabId) {\n' +
  '      try {\n' +
  '        // Get target tab dimensions\n' +
  '        const tabInfo = await this.getTargetTabInfo(targetTabId);\n' +
  '        if (!tabInfo) {\n' +
  '          console.error("[POC-Streaming] Could not get target tab info");\n' +
  '          return;\n' +
  '        }\n' +
  '\n' +
  '        // Calculate actual coordinates in target tab\n' +
  '        const targetX = userEvent.x * tabInfo.width;\n' +
  '        const targetY = userEvent.y * tabInfo.height;\n' +
  '\n' +
  '        console.log(\n' +
  '          `[POC-Streaming] Replaying click at (${targetX}, ${targetY}) on tab ${targetTabId}`\n' +
  '        );\n' +
  '\n' +
  '        // Use CDP Input API for more reliable event dispatch\n' +
  '        console.log("[POC-Streaming] Using CDP Input API for click event");\n' +
  '\n' +
  '        // Dispatch mouse down\n' +
  '        await this.executeCDPInputCommand(\n' +
  '          targetTabId,\n' +
  '          "Input.dispatchMouseEvent",\n' +
  '          {\n' +
  '            type: "mousePressed",\n' +
  '            x: Math.round(targetX),\n' +
  '            y: Math.round(targetY),\n' +
  '            button: "left",\n' +
  '            clickCount: 1,\n' +
  '            buttons: 1,\n' +
  '          }\n' +
  '        );\n' +
  '\n' +
  '        // Small delay between mouse down and up\n' +
  '        await new Promise((resolve) => setTimeout(resolve, 50));\n' +
  '\n' +
  '        // Dispatch mouse up\n' +
  '        await this.executeCDPInputCommand(\n' +
  '          targetTabId,\n' +
  '          "Input.dispatchMouseEvent",\n' +
  '          {\n' +
  '            type: "mouseReleased",\n' +
  '            x: Math.round(targetX),\n' +
  '            y: Math.round(targetY),\n' +
  '            button: "left",\n' +
  '            clickCount: 1,\n' +
  '            buttons: 0,\n' +
  '          }\n' +
  '        );\n' +
  '\n' +
  '        console.log(\n' +
  '          "[POC-Streaming] Click event dispatched successfully via CDP Input API"\n' +
  '        );\n' +
  '      } catch (error) {\n' +
  '        console.error("[POC-Streaming] Failed to replay click:", error);\n' +
  '      }\n' +
  '    }\n' +
  '\n' +
  '    /**\n' +
  '     * Get target tab information using simple-cdp\n' +
  '     */\n' +
  '    async getTargetTabInfo(targetTabId) {\n' +
  '      try {\n' +
  '        console.log("[POC-Streaming] Getting tab info for:", targetTabId);\n' +
  '\n' +
  '        const cdpClient = await this.establishCDPSession(targetTabId);\n' +
  '        const sessionId = this.cdpSessions.get(targetTabId);\n' +
  '\n' +
  '        // Use simple-cdp to evaluate script and get tab dimensions\n' +
  '        const result = await cdpClient.Runtime.evaluate(\n' +
  '          {\n' +
  '            expression: `({\n' +
  '              width: window.innerWidth,\n' +
  '              height: window.innerHeight,\n' +
  '              url: window.location.href\n' +
  '            })`,\n' +
  '            returnByValue: true,\n' +
  '            awaitPromise: true,\n' +
  '          },\n' +
  '          sessionId\n' +
  '        );\n' +
  '        return result.result.value;\n' +
  '      } catch (error) {\n' +
  '        console.error("[POC-Streaming] Failed to get tab info:", error);\n' +
  '        return { width: 1920, height: 1080, url: "unknown" };\n' +
  '      }\n' +
  '    }\n' +
  '\n' +
  '    /**\n' +
  '     * Execute script on target tab using simple-cdp\n' +
  '     */\n' +
  '    async executeCDPScript(targetTabId, script) {\n' +
  '      try {\n' +
  '        const cdpClient = await this.establishCDPSession(targetTabId);\n' +
  '        const sessionId = this.cdpSessions.get(targetTabId);\n' +
  '\n' +
  '        console.log(\n' +
  '          "[POC-Streaming] Executing script on target tab:",\n' +
  '          targetTabId\n' +
  '        );\n' +
  '\n' +
  '        // Use simple-cdp to evaluate script\n' +
  '        const result = await cdpClient.Runtime.evaluate(\n' +
  '          {\n' +
  '            expression: script,\n' +
  '            returnByValue: true,\n' +
  '          },\n' +
  '          sessionId\n' +
  '        );\n' +
  '\n' +
  '        return result.result.value;\n' +
  '      } catch (error) {\n' +
  '        console.error("[POC-Streaming] Failed to execute CDP script:", error);\n' +
  '        return null;\n' +
  '      }\n' +
  '    }\n' +
  '\n' +
  '    /**\n' +
  '     * Clean up CDP session for a target tab\n' +
  '     */\n' +
  '    async cleanupCDPSession(targetTabId) {\n' +
  '      try {\n' +
  '        const cdpClient = this.cdpClients.get(targetTabId);\n' +
  '        if (cdpClient) {\n' +
  '          console.log(\n' +
  '            "[POC-Streaming] Cleaning up CDP session for tab:",\n' +
  '            targetTabId\n' +
  '          );\n' +
  '          await cdpClient.close();\n' +
  '          this.cdpClients.delete(targetTabId);\n' +
  '          this.cdpSessions.delete(targetTabId);\n' +
  '        }\n' +
  '      } catch (error) {\n' +
  '        console.error("[POC-Streaming] Failed to cleanup CDP session:", error);\n' +
  '      }\n' +
  '    }\n' +
  '  }\n' +
  '\n' +
  '  // Initialize the control tab manager\n' +
  '  window.pocControlTabManager = new ControlTabManager();\n' +
  '\n' +
  '  console.log("[POC-Streaming] Control tab script initialized successfully");\n' +
  '})();';

const cTargetTabScript =
  '(() => {\n' +
  '  "use strict";\n' +
  '\n' +
  '  // Prevent multiple injections\n' +
  '  if (window.TargetTabStreamer) {\n' +
  '    console.log("[POC-Streamer] Target streamer already initialized");\n' +
  '    return;\n' +
  '  }\n' +
  '\n' +
  '  class TargetTabStreamer {\n' +
  '    constructor(signalingServerUrl, tabId, autoInitialize) {\n' +
  '      this.signalingServerUrl = signalingServerUrl;\n' +
  '      this.ws = null;\n' +
  '      this.clientId = null;\n' +
  '      this.tabId = tabId;\n' +
  '      this.isConnected = false;\n' +
  '      this.captureStream = null;\n' +
  '      this.peerConnections = new Map();\n' +
  '\n' +
  '      console.log("[POC-Streamer] Target tab streamer initializing...");\n' +
  '      this.init(autoInitialize === "true");\n' +
  '    }\n' +
  '\n' +
  '    async init(autoInitialize) {\n' +
  '      try {\n' +
  '        // Tab ID is already set in constructor\n' +
  '        console.log("[POC-Streamer] Using tab ID:", this.tabId);\n' +
  '\n' +
  '        // Connect to signaling server\n' +
  '        await this.connectToSignalingServer();\n' +
  '\n' +
  '        if (autoInitialize) {\n' +
  '          await this.handleStartStream();\n' +
  '        }\n' +
  '\n' +
  '        console.log(\n' +
  '          "[POC-Streamer] Target tab streamer initialized successfully"\n' +
  '        );\n' +
  '      } catch (error) {\n' +
  '        console.error(\n' +
  '          "[POC-Streamer] Failed to initialize target streamer:",\n' +
  '          error\n' +
  '        );\n' +
  '      }\n' +
  '    }\n' +
  '\n' +
  '    getTabId() {\n' +
  '      // Try to get tab ID from various sources\n' +
  '      const urlParams = new URLSearchParams(window.location.search);\n' +
  '      const tabId =\n' +
  '        urlParams.get("tabId") ||\n' +
  '        window.name ||\n' +
  '        document.title ||\n' +
  '        window.location.hostname ||\n' +
  '        this.generateId();\n' +
  '\n' +
  '      console.log("[POC-Streamer] Tab ID:", tabId);\n' +
  '      return tabId;\n' +
  '    }\n' +
  '\n' +
  '    async connectToSignalingServer() {\n' +
  '      return new Promise((resolve, reject) => {\n' +
  '        this.ws = new WebSocket(this.signalingServerUrl);\n' +
  '\n' +
  '        this.ws.onopen = () => {\n' +
  '          console.log("[POC-Streamer] Connected to signaling server");\n' +
  '          this.isConnected = true;\n' +
  '\n' +
  '          // Register as target tab\n' +
  '          this.sendMessage({\n' +
  '            type: "register-target-tab",\n' +
  '            tabId: this.tabId,\n' +
  '            url: window.location.href,\n' +
  '            title: document.title,\n' +
  '            metadata: {\n' +
  '              userAgent: navigator.userAgent,\n' +
  '              timestamp: Date.now(),\n' +
  '            },\n' +
  '          });\n' +
  '\n' +
  '          resolve();\n' +
  '        };\n' +
  '\n' +
  '        this.ws.onmessage = (event) => {\n' +
  '          try {\n' +
  '            const message = JSON.parse(event.data);\n' +
  '            this.handleMessage(message);\n' +
  '          } catch (error) {\n' +
  '            console.error("[POC-Streamer] Failed to parse message:", error);\n' +
  '          }\n' +
  '        };\n' +
  '\n' +
  '        this.ws.onclose = () => {\n' +
  '          console.log("[POC-Streamer] Disconnected from signaling server");\n' +
  '          this.isConnected = false;\n' +
  '\n' +
  '          // Attempt to reconnect after delay\n' +
  '          setTimeout(() => {\n' +
  '            if (!this.isConnected) {\n' +
  '              console.log("[POC-Streamer] Attempting to reconnect...");\n' +
  '              this.connectToSignalingServer().catch(console.error);\n' +
  '            }\n' +
  '          }, 5000);\n' +
  '        };\n' +
  '\n' +
  '        this.ws.onerror = (error) => {\n' +
  '          console.error("[POC-Streamer] WebSocket error:", error);\n' +
  '          reject(error);\n' +
  '        };\n' +
  '\n' +
  '        // Timeout for connection\n' +
  '        setTimeout(() => {\n' +
  '          if (!this.isConnected) {\n' +
  '            reject(new Error("Connection timeout"));\n' +
  '          }\n' +
  '        }, 10000);\n' +
  '      });\n' +
  '    }\n' +
  '\n' +
  '    async handleMessage(message) {\n' +
  '      console.log("[POC-Streamer] Received message:", message.type);\n' +
  '\n' +
  '      switch (message.type) {\n' +
  '        case "welcome":\n' +
  '          this.clientId = message.clientId;\n' +
  '          console.log(\n' +
  '            "[POC-Streamer] Registered with client ID:",\n' +
  '            this.clientId\n' +
  '          );\n' +
  '          break;\n' +
  '\n' +
  '        case "start-streaming":\n' +
  '          await this.handleStartStream(message);\n' +
  '          break;\n' +
  '\n' +
  '        case "webrtc-answer-to-target":\n' +
  '          await this.handleWebRTCAnswer(message);\n' +
  '          break;\n' +
  '\n' +
  '        case "webrtc-ice-candidate-to-target":\n' +
  '          await this.handleICECandidate(message);\n' +
  '          break;\n' +
  '        case "stop-streaming":\n' +
  '          // await this.handleStopStream(message);\n' +
  '          break;\n' +
  '        default:\n' +
  '          console.log("[POC-Streamer] Unhandled message type:", message.type);\n' +
  '      }\n' +
  '    }\n' +
  '\n' +
  '    async handleStartStream() {\n' +
  '      try {\n' +
  '        console.log("[POC-Streamer] Starting stream for:", this.tabId);\n' +
  '\n' +
  '        // Capture current tab content automatically\n' +
  '        const stream = await this.captureCurrentTab();\n' +
  '        this.captureStream = stream;\n' +
  '\n' +
  '        // Create peer connection for this stream\n' +
  '        const peerConnection = await this.createPeerConnection();\n' +
  '\n' +
  '        // Add stream tracks to peer connection\n' +
  '        stream.getTracks().forEach((track) => {\n' +
  '          peerConnection.addTrack(track, stream);\n' +
  '        });\n' +
  '\n' +
  '        // Create and send offer\n' +
  '        const offer = await peerConnection.createOffer();\n' +
  '        await peerConnection.setLocalDescription(offer);\n' +
  '\n' +
  '        this.sendMessage({\n' +
  '          type: "webrtc-offer-from-target",\n' +
  '          offer: offer,\n' +
  '          targetTabId: this.tabId,\n' +
  '        });\n' +
  '\n' +
  '        // Notify server that streaming is ready\n' +
  '        this.sendMessage({\n' +
  '          type: "streaming-ready",\n' +
  '          tabId: this.tabId,\n' +
  '        });\n' +
  '\n' +
  '        console.log("[POC-Streamer] Stream started and offer sent");\n' +
  '      } catch (error) {\n' +
  '        console.error("[POC-Streamer] Failed to start stream:", error);\n' +
  '\n' +
  '        // Send error back to signaling server\n' +
  '        this.sendMessage({\n' +
  '          type: "stream-error",\n' +
  '          tabId: this.tabId,\n' +
  '          error: error.message,\n' +
  '        });\n' +
  '      }\n' +
  '    }\n' +
  '\n' +
  '    async captureCurrentTab() {\n' +
  '      try {\n' +
  '        console.log("[POC-Streamer] Capturing current tab content...");\n' +
  '\n' +
  '        // Use getDisplayMedia with preferCurrentTab for automatic capture\n' +
  '        const stream = await navigator.mediaDevices.getDisplayMedia({\n' +
  '          video: {\n' +
  '            width: { ideal: 1280 },\n' +
  '            height: { ideal: 720 },\n' +
  '            frameRate: { ideal: 100 },\n' +
  '          },\n' +
  '          audio: false, // Disable audio for simplicity\n' +
  '          preferCurrentTab: true, // Automatically capture this tab\n' +
  '        });\n' +
  '\n' +
  '        console.log("[POC-Streamer] Tab content captured successfully");\n' +
  '        console.log("[POC-Streamer] Stream active:", stream.active);\n' +
  '        console.log("[POC-Streamer] Stream tracks:", stream.getTracks());\n' +
  '\n' +
  '        // Check each track\n' +
  '        stream.getTracks().forEach((track, index) => {\n' +
  '          console.log(`[POC-Streamer] Track ${index}:`, {\n' +
  '            kind: track.kind,\n' +
  '            enabled: track.enabled,\n' +
  '            muted: track.muted,\n' +
  '            readyState: track.readyState,\n' +
  '            label: track.label,\n' +
  '          });\n' +
  '        });\n' +
  '\n' +
  '        return stream;\n' +
  '      } catch (error) {\n' +
  '        console.error("[POC-Streamer] Failed to capture tab content:", error);\n' +
  '\n' +
  '        throw error;\n' +
  '      }\n' +
  '    }\n' +
  '\n' +
  '    createFallbackStream() {\n' +
  '      console.log("[POC-Streamer] Creating fallback stream...");\n' +
  '\n' +
  '      const canvas = document.createElement("canvas");\n' +
  '      canvas.width = 640;\n' +
  '      canvas.height = 480;\n' +
  '      const ctx = canvas.getContext("2d");\n' +
  '\n' +
  '      // Create animated fallback content\n' +
  '      let frame = 0;\n' +
  '      const animate = () => {\n' +
  '        ctx.fillStyle = "#2196F3";\n' +
  '        ctx.fillRect(0, 0, canvas.width, canvas.height);\n' +
  '\n' +
  '        ctx.fillStyle = "white";\n' +
  '        ctx.font = "24px Arial";\n' +
  '        ctx.textAlign = "center";\n' +
  '        ctx.fillText(\n' +
  '          "Tab Content Capture",\n' +
  '          canvas.width / 2,\n' +
  '          canvas.height / 2 - 40\n' +
  '        );\n' +
  '        ctx.fillText(`${document.title}`, canvas.width / 2, canvas.height / 2);\n' +
  '        ctx.fillText(\n' +
  '          `Frame: ${frame++}`,\n' +
  '          canvas.width / 2,\n' +
  '          canvas.height / 2 + 40\n' +
  '        );\n' +
  '\n' +
  '        requestAnimationFrame(animate);\n' +
  '      };\n' +
  '      animate();\n' +
  '\n' +
  '      return canvas.captureStream(30);\n' +
  '    }\n' +
  '\n' +
  '    async createPeerConnection() {\n' +
  '      const peerConnection = new RTCPeerConnection({\n' +
  '        iceServers: [{ urls: "stun:stun.l.google.com:19302" }],\n' +
  '      });\n' +
  '\n' +
  '      // Store peer connection using tabId as key\n' +
  '      this.peerConnections.set(this.tabId, peerConnection);\n' +
  '\n' +
  '      // Handle ICE candidates\n' +
  '      peerConnection.onicecandidate = (event) => {\n' +
  '        if (event.candidate) {\n' +
  '          this.sendMessage({\n' +
  '            type: "webrtc-ice-candidate-from-target",\n' +
  '            candidate: event.candidate,\n' +
  '            targetTabId: this.tabId,\n' +
  '          });\n' +
  '        }\n' +
  '      };\n' +
  '\n' +
  '      // Handle connection state changes\n' +
  '      peerConnection.onconnectionstatechange = () => {\n' +
  '        console.log(\n' +
  '          "[POC-Streamer] Connection state:",\n' +
  '          peerConnection.connectionState\n' +
  '        );\n' +
  '      };\n' +
  '\n' +
  '      return peerConnection;\n' +
  '    }\n' +
  '\n' +
  '    async handleWebRTCAnswer(message) {\n' +
  '      const peerConnection = this.peerConnections.get(message.targetTabId);\n' +
  '      if (peerConnection) {\n' +
  '        await peerConnection.setRemoteDescription(message.answer);\n' +
  '        console.log("[POC-Streamer] WebRTC answer processed");\n' +
  '      }\n' +
  '    }\n' +
  '\n' +
  '    async handleICECandidate(message) {\n' +
  '      const peerConnection = this.peerConnections.get(message.targetTabId);\n' +
  '      if (peerConnection) {\n' +
  '        await peerConnection.addIceCandidate(message.candidate);\n' +
  '      }\n' +
  '    }\n' +
  '\n' +
  '    sendMessage(message) {\n' +
  '      if (this.ws && this.ws.readyState === WebSocket.OPEN) {\n' +
  '        this.ws.send(JSON.stringify(message));\n' +
  '      }\n' +
  '    }\n' +
  '\n' +
  '    generateId() {\n' +
  '      return Math.random().toString(36).substr(2, 9);\n' +
  '    }\n' +
  '\n' +
  '    cleanup() {\n' +
  '      console.log("[POC-Streamer] Cleaning up target streamer...");\n' +
  '\n' +
  '      // Stop capture stream\n' +
  '      if (this.captureStream) {\n' +
  '        this.captureStream.getTracks().forEach((track) => track.stop());\n' +
  '      }\n' +
  '\n' +
  '      // Close peer connections\n' +
  '      for (const pc of this.peerConnections.values()) {\n' +
  '        pc.close();\n' +
  '      }\n' +
  '      this.peerConnections.clear();\n' +
  '\n' +
  '      // Close WebSocket\n' +
  '      if (this.ws) {\n' +
  '        this.ws.close();\n' +
  '      }\n' +
  '    }\n' +
  '  }\n' +
  '\n' +
  '  // Initialize the target tab streamer\n' +
  '  const signalingServerUrl = "${SIGNALING_SERVER_URL}";\n' +
  '  const tabId = "${TAB_ID}";\n' +
  '  const autoInitialize = "${AUTO_INITIALIZE}";\n' +
  '  window.TargetTabStreamer = new TargetTabStreamer(\n' +
  '    signalingServerUrl,\n' +
  '    tabId,\n' +
  '    autoInitialize\n' +
  '  );\n' +
  '\n' +
  '  // Cleanup on page unload\n' +
  '  window.addEventListener("beforeunload", () => {\n' +
  '    if (window.TargetTabStreamer) {\n' +
  '      window.TargetTabStreamer.cleanup();\n' +
  '    }\n' +
  '  });\n' +
  '})();';
